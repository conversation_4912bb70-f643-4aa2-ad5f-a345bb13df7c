"""
AG-UI Integration Implementation Code Examples
Detailed code implementations for integrating AG-UI protocol with MCP Financial Analyzer
"""

from abc import abstractmethod
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import asyncio
import json
from dataclasses import dataclass
from enum import Enum

# Mock AG-UI imports (replace with actual AG-UI package imports)
class EventType(Enum):
    RUN_STARTED = "RUN_STARTED"
    RUN_FINISHED = "RUN_FINISHED" 
    RUN_ERROR = "RUN_ERROR"
    TEXT_MESSAGE_START = "TEXT_MESSAGE_START"
    TEXT_MESSAGE_CONTENT = "TEXT_MESSAGE_CONTENT"
    TEXT_MESSAGE_END = "TEXT_MESSAGE_END"
    TOOL_CALL_START = "TOOL_CALL_START"
    TOOL_CALL_ARGS = "TOOL_CALL_ARGS"
    TOOL_CALL_END = "TOOL_CALL_END"
    STATE_SNAPSHOT = "STATE_SNAPSHOT"
    STATE_DELTA = "STATE_DELTA"

@dataclass
class BaseEvent:
    type: EventType
    timestamp: str
    threadId: Optional[str] = None
    runId: Optional[str] = None

@dataclass
class RunAgentInput:
    threadId: str
    runId: str
    messages: List[Dict]
    state: Optional[Dict] = None
    tools: Optional[List[Dict]] = None

# Mock Observable class (replace with actual RxJS Observable)
class Observable:
    def __init__(self, subscribe_func):
        self.subscribe_func = subscribe_func
    
    def subscribe(self, observer):
        return self.subscribe_func(observer)

class AbstractAgent:
    """Mock AbstractAgent base class"""
    def __init__(self):
        self.state = {}
        self.messages = []
    
    @abstractmethod
    def run(self, input: RunAgentInput):
        pass

# =============================================================================
# 1. AG-UI Financial Agent Base Implementation
# =============================================================================

class AGUIFinancialAgent(AbstractAgent):
    """
    Base AG-UI agent for financial analysis workflows.
    Extends AbstractAgent with financial analysis specific capabilities.
    """
    
    def __init__(self, mcp_agent_wrapper, name: str):
        super().__init__()
        self.mcp_agent = mcp_agent_wrapper
        self.name = name
        self.analysis_state = {
            "company_name": None,
            "analysis_phase": "initialized",
            "progress_percentage": 0.0,
            "current_operation": None,
            "research_data": None,
            "analysis_results": None,
            "error_state": None
        }
    
    def run(self, input: RunAgentInput):
        """
        Main AG-UI run method that returns Observable for streaming events.
        """
        return lambda: Observable(lambda observer: self._execute_workflow(input, observer))
    
    async def _execute_workflow(self, input: RunAgentInput, observer):
        """
        Execute the financial analysis workflow with AG-UI event streaming.
        """
        try:
            # Emit RUN_STARTED event
            await self._emit_run_started(input, observer)
            
            # Execute the specific agent workflow
            await self._execute_agent_logic(input, observer)
            
            # Emit RUN_FINISHED event
            await self._emit_run_finished(input, observer)
            
        except Exception as e:
            # Emit RUN_ERROR event
            await self._emit_run_error(input, observer, str(e))
        finally:
            observer.complete()
    
    async def _emit_run_started(self, input: RunAgentInput, observer):
        """Emit RUN_STARTED event with workflow metadata"""
        event = {
            "type": EventType.RUN_STARTED.value,
            "threadId": input.threadId,
            "runId": input.runId,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "metadata": {
                "agent_name": self.name,
                "company_name": self.analysis_state.get("company_name"),
                "analysis_type": "financial_analysis"
            }
        }
        observer.next(event)
    
    async def _emit_run_finished(self, input: RunAgentInput, observer):
        """Emit RUN_FINISHED event with completion metadata"""
        event = {
            "type": EventType.RUN_FINISHED.value,
            "threadId": input.threadId,
            "runId": input.runId,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "metadata": {
                "agent_name": self.name,
                "completion_status": "success",
                "final_progress": 100.0
            }
        }
        observer.next(event)
    
    async def _emit_run_error(self, input: RunAgentInput, observer, error_message: str):
        """Emit RUN_ERROR event with error details"""
        event = {
            "type": EventType.RUN_ERROR.value,
            "threadId": input.threadId,
            "runId": input.runId,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "error": {
                "code": "AGENT_EXECUTION_ERROR",
                "message": error_message,
                "agent_name": self.name
            }
        }
        observer.next(event)
    
    @abstractmethod
    async def _execute_agent_logic(self, input: RunAgentInput, observer):
        """Implement specific agent logic in subclasses"""
        pass
    
    async def _emit_text_message(self, content: str, observer, message_id: str = None):
        """Helper method to emit streaming text messages"""
        if message_id is None:
            message_id = f"msg_{datetime.utcnow().timestamp()}"
        
        # Emit TEXT_MESSAGE_START
        start_event = {
            "type": EventType.TEXT_MESSAGE_START.value,
            "messageId": message_id,
            "role": "assistant",
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        observer.next(start_event)
        
        # Stream content in chunks
        chunk_size = 50  # Adjust for optimal streaming
        for i in range(0, len(content), chunk_size):
            chunk = content[i:i + chunk_size]
            content_event = {
                "type": EventType.TEXT_MESSAGE_CONTENT.value,
                "messageId": message_id,
                "delta": chunk,
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }
            observer.next(content_event)
            await asyncio.sleep(0.01)  # Small delay for streaming effect
        
        # Emit TEXT_MESSAGE_END
        end_event = {
            "type": EventType.TEXT_MESSAGE_END.value,
            "messageId": message_id,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        observer.next(end_event)
    
    async def _emit_state_update(self, updates: Dict, observer, input: RunAgentInput):
        """Emit STATE_DELTA event with incremental state updates"""
        # Update internal state
        self.analysis_state.update(updates)
        
        # Generate JSON Patch operations
        delta_operations = []
        for key, value in updates.items():
            delta_operations.append({
                "op": "replace",
                "path": f"/{key}",
                "value": value
            })
        
        # Emit STATE_DELTA event
        event = {
            "type": EventType.STATE_DELTA.value,
            "threadId": input.threadId,
            "delta": delta_operations,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        observer.next(event)

# =============================================================================
# 2. Research Agent AG-UI Implementation
# =============================================================================

class AGUIResearchAgent(AGUIFinancialAgent):
    """
    AG-UI wrapper for research agent with streaming financial data collection.
    """
    
    def __init__(self, mcp_research_agent, company_name: str):
        super().__init__(mcp_research_agent, "financial_research_agent")
        self.analysis_state["company_name"] = company_name
    
    async def _execute_agent_logic(self, input: RunAgentInput, observer):
        """Execute financial research with streaming events"""
        
        # Update state to research phase
        await self._emit_state_update({
            "analysis_phase": "research",
            "current_operation": "financial_data_collection",
            "progress_percentage": 10.0
        }, observer, input)
        
        # Execute search operations with tool call events
        search_queries = [
            f"{self.analysis_state['company_name']} stock price today",
            f"{self.analysis_state['company_name']} latest quarterly earnings",
            f"{self.analysis_state['company_name']} financial news recent"
        ]
        
        research_results = []
        for i, query in enumerate(search_queries):
            # Emit tool call for search operation
            search_result = await self._execute_search_tool(query, observer, input)
            research_results.append(search_result)
            
            # Update progress
            progress = 10.0 + (i + 1) * 20.0
            await self._emit_state_update({
                "progress_percentage": progress
            }, observer, input)
        
        # Compile research results
        compiled_research = self._compile_research_results(research_results)
        
        # Stream research findings
        await self._emit_text_message(
            f"Research completed for {self.analysis_state['company_name']}:\n\n{compiled_research}",
            observer
        )
        
        # Update final state
        await self._emit_state_update({
            "analysis_phase": "research_complete",
            "research_data": compiled_research,
            "progress_percentage": 70.0,
            "current_operation": "research_compilation"
        }, observer, input)
    
    async def _execute_search_tool(self, query: str, observer, input: RunAgentInput) -> str:
        """Execute search tool with AG-UI tool call events"""
        tool_call_id = f"search_{datetime.utcnow().timestamp()}"
        
        # Emit TOOL_CALL_START
        start_event = {
            "type": EventType.TOOL_CALL_START.value,
            "toolCallId": tool_call_id,
            "toolCallName": "search_financial_data",
            "parentMessageId": f"msg_research_{input.runId}",
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        observer.next(start_event)
        
        # Emit TOOL_CALL_ARGS with search parameters
        args_event = {
            "type": EventType.TOOL_CALL_ARGS.value,
            "toolCallId": tool_call_id,
            "delta": json.dumps({
                "query": query,
                "search_type": "financial_data",
                "company": self.analysis_state["company_name"]
            }),
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        observer.next(args_event)
        
        # Execute actual MCP search (mock implementation)
        await asyncio.sleep(1)  # Simulate search delay
        search_result = f"Mock search result for: {query}"
        
        # Emit TOOL_CALL_END with results
        end_event = {
            "type": EventType.TOOL_CALL_END.value,
            "toolCallId": tool_call_id,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "result": search_result
        }
        observer.next(end_event)
        
        return search_result
    
    def _compile_research_results(self, results: List[str]) -> str:
        """Compile individual search results into comprehensive research summary"""
        return "\n\n".join([f"Finding {i+1}: {result}" for i, result in enumerate(results)])

# =============================================================================
# 3. Analysis Agent AG-UI Implementation  
# =============================================================================

class AGUIAnalystAgent(AGUIFinancialAgent):
    """
    AG-UI wrapper for financial analysis agent with real-time streaming insights.
    """
    
    def __init__(self, mcp_analyst_agent, company_name: str):
        super().__init__(mcp_analyst_agent, "financial_analyst_agent")
        self.analysis_state["company_name"] = company_name
    
    async def _execute_agent_logic(self, input: RunAgentInput, observer):
        """Execute financial analysis with streaming insights"""
        
        # Get research data from state or input
        research_data = self.analysis_state.get("research_data") or "Mock research data"
        
        # Update state to analysis phase
        await self._emit_state_update({
            "analysis_phase": "analysis",
            "current_operation": "financial_metrics_analysis",
            "progress_percentage": 75.0
        }, observer, input)
        
        # Perform analysis with streaming results
        analysis_sections = [
            "Stock Performance Analysis",
            "Earnings Analysis", 
            "Market Position Assessment",
            "Risk Factors Evaluation",
            "Investment Recommendations"
        ]
        
        analysis_results = {}
        for i, section in enumerate(analysis_sections):
            # Stream analysis section
            section_analysis = await self._analyze_section(section, research_data, observer)
            analysis_results[section.lower().replace(" ", "_")] = section_analysis
            
            # Update progress
            progress = 75.0 + (i + 1) * 4.0
            await self._emit_state_update({
                "progress_percentage": progress,
                "current_operation": f"analyzing_{section.lower().replace(' ', '_')}"
            }, observer, input)
        
        # Final analysis state update
        await self._emit_state_update({
            "analysis_phase": "analysis_complete",
            "analysis_results": analysis_results,
            "progress_percentage": 95.0,
            "current_operation": "analysis_finalization"
        }, observer, input)
    
    async def _analyze_section(self, section: str, research_data: str, observer) -> str:
        """Analyze specific section with streaming output"""
        analysis_content = f"Analysis of {section} based on research data:\n"
        analysis_content += f"Key insights and findings for this section..."
        
        # Stream the analysis content
        await self._emit_text_message(
            f"📊 {section}:\n{analysis_content}\n",
            observer
        )
        
        return analysis_content

# =============================================================================
# 4. Report Writer AG-UI Implementation
# =============================================================================

class AGUIReportWriter(AGUIFinancialAgent):
    """
    AG-UI wrapper for report writer with streaming report generation.
    """

    def __init__(self, mcp_report_writer, company_name: str, output_path: str):
        super().__init__(mcp_report_writer, "financial_report_writer")
        self.analysis_state["company_name"] = company_name
        self.output_path = output_path

    async def _execute_agent_logic(self, input: RunAgentInput, observer):
        """Execute report generation with streaming progress"""

        # Get analysis data from state
        analysis_data = self.analysis_state.get("analysis_results") or "Mock analysis data"

        # Update state to reporting phase
        await self._emit_state_update({
            "analysis_phase": "reporting",
            "current_operation": "report_generation",
            "progress_percentage": 95.0
        }, observer, input)

        # Generate report with file writing tool call
        await self._execute_report_generation_tool(analysis_data, observer, input)

        # Final completion
        await self._emit_state_update({
            "analysis_phase": "complete",
            "current_operation": "workflow_complete",
            "progress_percentage": 100.0
        }, observer, input)

    async def _execute_report_generation_tool(self, analysis_data: str, observer, input: RunAgentInput):
        """Execute report generation tool with AG-UI events"""
        tool_call_id = f"report_gen_{datetime.utcnow().timestamp()}"

        # Emit TOOL_CALL_START
        start_event = {
            "type": EventType.TOOL_CALL_START.value,
            "toolCallId": tool_call_id,
            "toolCallName": "generate_financial_report",
            "parentMessageId": f"msg_report_{input.runId}",
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        observer.next(start_event)

        # Emit TOOL_CALL_ARGS with report parameters
        args_event = {
            "type": EventType.TOOL_CALL_ARGS.value,
            "toolCallId": tool_call_id,
            "delta": json.dumps({
                "company_name": self.analysis_state["company_name"],
                "analysis_data": analysis_data,
                "output_path": self.output_path,
                "format": "markdown"
            }),
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        observer.next(args_event)

        # Generate report content
        report_content = self._generate_report_content(analysis_data)

        # Stream report generation progress
        await self._emit_text_message(
            f"📄 Generating comprehensive financial report for {self.analysis_state['company_name']}...\n"
            f"Report will be saved to: {self.output_path}",
            observer
        )

        # Simulate file writing
        await asyncio.sleep(1)

        # Emit TOOL_CALL_END with success
        end_event = {
            "type": EventType.TOOL_CALL_END.value,
            "toolCallId": tool_call_id,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "result": {
                "status": "success",
                "file_path": self.output_path,
                "word_count": len(report_content.split())
            }
        }
        observer.next(end_event)

    def _generate_report_content(self, analysis_data: str) -> str:
        """Generate formatted report content"""
        return f"""# Financial Analysis Report: {self.analysis_state['company_name']}

## Executive Summary
{analysis_data}

## Detailed Analysis
[Comprehensive analysis sections...]

## Recommendations
[Investment recommendations...]

---
Report generated on {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC
"""

# =============================================================================
# 5. AG-UI State Manager Implementation
# =============================================================================

class AGUIStateManager:
    """
    Manages AG-UI state for financial analysis workflow with JSON Patch support.
    """

    def __init__(self):
        self.current_state = {
            "company_name": None,
            "analysis_phase": "initialized",
            "progress_percentage": 0.0,
            "current_operation": None,
            "research_data": None,
            "analysis_results": None,
            "error_state": None,
            "workflow_metadata": {
                "start_time": None,
                "estimated_completion": None,
                "agent_sequence": []
            }
        }
        self.state_history = []

    def update_state(self, updates: Dict[str, Any], observer, thread_id: str):
        """
        Update state and emit STATE_DELTA event with JSON Patch operations.
        """
        # Store previous state for history
        previous_state = self.current_state.copy()
        self.state_history.append(previous_state)

        # Apply updates
        self.current_state.update(updates)

        # Generate JSON Patch operations
        delta_operations = self._generate_json_patch(previous_state, self.current_state)

        # Emit STATE_DELTA event
        event = {
            "type": EventType.STATE_DELTA.value,
            "threadId": thread_id,
            "delta": delta_operations,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        observer.next(event)

    def snapshot_state(self, observer, thread_id: str):
        """
        Emit complete state snapshot via STATE_SNAPSHOT event.
        """
        event = {
            "type": EventType.STATE_SNAPSHOT.value,
            "threadId": thread_id,
            "state": self.current_state.copy(),
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        observer.next(event)

    def _generate_json_patch(self, old_state: Dict, new_state: Dict) -> List[Dict]:
        """
        Generate JSON Patch operations for state changes.
        Simplified implementation - use jsonpatch library for production.
        """
        operations = []

        for key, new_value in new_state.items():
            old_value = old_state.get(key)
            if old_value != new_value:
                operations.append({
                    "op": "replace",
                    "path": f"/{key}",
                    "value": new_value
                })

        return operations

    def get_state_summary(self) -> Dict[str, Any]:
        """Get summary of current state for monitoring"""
        return {
            "phase": self.current_state["analysis_phase"],
            "progress": self.current_state["progress_percentage"],
            "operation": self.current_state["current_operation"],
            "has_errors": self.current_state["error_state"] is not None,
            "history_length": len(self.state_history)
        }

# =============================================================================
# 6. Tool Bridge Implementation
# =============================================================================

class MCPToAGUIToolBridge:
    """
    Bridges MCP tool execution to AG-UI tool call events.
    Maps existing MCP tools to AG-UI streaming tool calls.
    """

    def __init__(self):
        self.tool_mapping = {
            "g-search": "search_financial_data",
            "fetch": "fetch_financial_document",
            "filesystem": "save_analysis_report"
        }

    async def execute_mcp_tool_with_agui_events(
        self,
        mcp_tool_name: str,
        tool_args: Dict[str, Any],
        observer,
        parent_message_id: str = None
    ) -> Any:
        """
        Execute MCP tool while emitting AG-UI tool call events.
        """
        # Generate tool call ID
        tool_call_id = f"{mcp_tool_name}_{datetime.utcnow().timestamp()}"
        agui_tool_name = self.tool_mapping.get(mcp_tool_name, mcp_tool_name)

        try:
            # Emit TOOL_CALL_START
            await self._emit_tool_call_start(
                tool_call_id, agui_tool_name, parent_message_id, observer
            )

            # Emit TOOL_CALL_ARGS
            await self._emit_tool_call_args(tool_call_id, tool_args, observer)

            # Execute actual MCP tool (mock implementation)
            result = await self._execute_mcp_tool(mcp_tool_name, tool_args)

            # Emit TOOL_CALL_END with success
            await self._emit_tool_call_end(tool_call_id, result, observer)

            return result

        except Exception as e:
            # Emit TOOL_CALL_END with error
            await self._emit_tool_call_end(tool_call_id, None, observer, str(e))
            raise

    async def _emit_tool_call_start(
        self,
        tool_call_id: str,
        tool_name: str,
        parent_message_id: str,
        observer
    ):
        """Emit TOOL_CALL_START event"""
        event = {
            "type": EventType.TOOL_CALL_START.value,
            "toolCallId": tool_call_id,
            "toolCallName": tool_name,
            "parentMessageId": parent_message_id,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        observer.next(event)

    async def _emit_tool_call_args(
        self,
        tool_call_id: str,
        args: Dict[str, Any],
        observer
    ):
        """Emit TOOL_CALL_ARGS event with arguments"""
        event = {
            "type": EventType.TOOL_CALL_ARGS.value,
            "toolCallId": tool_call_id,
            "delta": json.dumps(args),
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        observer.next(event)

    async def _emit_tool_call_end(
        self,
        tool_call_id: str,
        result: Any,
        observer,
        error: str = None
    ):
        """Emit TOOL_CALL_END event with results or error"""
        event = {
            "type": EventType.TOOL_CALL_END.value,
            "toolCallId": tool_call_id,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }

        if error:
            event["error"] = error
        else:
            event["result"] = result

        observer.next(event)

    async def _execute_mcp_tool(self, tool_name: str, args: Dict[str, Any]) -> Any:
        """
        Execute actual MCP tool (mock implementation).
        Replace with actual MCP tool execution logic.
        """
        await asyncio.sleep(0.5)  # Simulate tool execution time

        if tool_name == "g-search":
            return f"Search results for: {args.get('query', 'unknown query')}"
        elif tool_name == "fetch":
            return f"Fetched content from: {args.get('url', 'unknown URL')}"
        elif tool_name == "filesystem":
            return f"File operation completed: {args.get('operation', 'unknown')}"
        else:
            return f"Tool {tool_name} executed with args: {args}"

# =============================================================================
# 7. Main AG-UI Integration Orchestrator
# =============================================================================

class AGUIFinancialAnalysisOrchestrator:
    """
    Main orchestrator that coordinates AG-UI agents for complete financial analysis.
    """

    def __init__(self, company_name: str, output_path: str):
        self.company_name = company_name
        self.output_path = output_path
        self.state_manager = AGUIStateManager()
        self.tool_bridge = MCPToAGUIToolBridge()

        # Initialize AG-UI agents (with mock MCP agents)
        self.research_agent = AGUIResearchAgent(None, company_name)
        self.analyst_agent = AGUIAnalystAgent(None, company_name)
        self.report_writer = AGUIReportWriter(None, company_name, output_path)

    def run_complete_analysis(self, input: RunAgentInput):
        """
        Run complete financial analysis workflow with AG-UI streaming.
        """
        return lambda: Observable(lambda observer: self._execute_complete_workflow(input, observer))

    async def _execute_complete_workflow(self, input: RunAgentInput, observer):
        """Execute complete workflow with coordinated agent execution"""
        try:
            # Initialize workflow state
            self.state_manager.update_state({
                "company_name": self.company_name,
                "analysis_phase": "workflow_started",
                "workflow_metadata": {
                    "start_time": datetime.utcnow().isoformat(),
                    "agent_sequence": ["research", "analysis", "reporting"]
                }
            }, observer, input.threadId)

            # Execute research phase
            await self._execute_agent_phase(self.research_agent, input, observer)

            # Execute analysis phase
            await self._execute_agent_phase(self.analyst_agent, input, observer)

            # Execute reporting phase
            await self._execute_agent_phase(self.report_writer, input, observer)

            # Final state snapshot
            self.state_manager.snapshot_state(observer, input.threadId)

        except Exception as e:
            # Handle workflow errors
            await self._handle_workflow_error(e, input, observer)
        finally:
            observer.complete()

    async def _execute_agent_phase(self, agent: AGUIFinancialAgent, input: RunAgentInput, observer):
        """Execute individual agent phase within the workflow"""
        # Share state between agents
        agent.analysis_state.update(self.state_manager.current_state)

        # Execute agent with its own observer wrapper
        agent_observable = agent.run(input)()

        # Subscribe to agent events and forward to main observer
        def forward_event(event):
            observer.next(event)
            # Update state manager if it's a state event
            if event.get("type") == EventType.STATE_DELTA.value:
                # Apply state updates from agent
                for op in event.get("delta", []):
                    if op["op"] == "replace":
                        key = op["path"].lstrip("/")
                        self.state_manager.current_state[key] = op["value"]

        agent_observable.subscribe(forward_event)

    async def _handle_workflow_error(self, error: Exception, input: RunAgentInput, observer):
        """Handle workflow-level errors"""
        self.state_manager.update_state({
            "error_state": str(error),
            "analysis_phase": "error"
        }, observer, input.threadId)

        # Emit workflow error event
        error_event = {
            "type": EventType.RUN_ERROR.value,
            "threadId": input.threadId,
            "runId": input.runId,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "error": {
                "code": "WORKFLOW_ERROR",
                "message": str(error),
                "phase": self.state_manager.current_state["analysis_phase"]
            }
        }
        observer.next(error_event)

# =============================================================================
# 8. Usage Example
# =============================================================================

async def example_usage():
    """Example of how to use the AG-UI financial analysis system"""

    # Create orchestrator
    orchestrator = AGUIFinancialAnalysisOrchestrator(
        company_name="Apple Inc.",
        output_path="./reports/apple_analysis.md"
    )

    # Create input
    input_data = RunAgentInput(
        threadId="financial_analysis_001",
        runId="run_001",
        messages=[{
            "role": "user",
            "content": "Analyze Apple Inc. stock performance and generate a comprehensive report"
        }],
        state={},
        tools=[]
    )

    # Execute analysis with event streaming
    analysis_observable = orchestrator.run_complete_analysis(input_data)()

    # Subscribe to events
    def handle_event(event):
        print(f"Event: {event['type']} - {event.get('timestamp', 'No timestamp')}")
        if event['type'] == EventType.TEXT_MESSAGE_CONTENT.value:
            print(f"Content: {event.get('delta', '')}")
        elif event['type'] == EventType.STATE_DELTA.value:
            print(f"State Update: {event.get('delta', [])}")

    analysis_observable.subscribe(handle_event)

if __name__ == "__main__":
    # Run example
    asyncio.run(example_usage())
