# AG-UI Integration Testing Strategy

## 1. Testing Overview

### 1.1 Testing Objectives

**Primary Goals:**
- Validate AG-UI event generation and streaming functionality
- Ensure backward compatibility with existing MCP Financial Analyzer
- Verify real-time state synchronization accuracy
- Confirm error handling and recovery mechanisms
- Validate performance requirements and scalability

**Success Criteria:**
- ✅ All AG-UI events generated correctly with proper schema validation
- ✅ Real-time streaming maintains <100ms average latency
- ✅ State synchronization accuracy >99% across all workflow phases
- ✅ Error recovery completes within 5 seconds for all error types
- ✅ Backward compatibility: existing MCP functionality unchanged
- ✅ Performance: memory usage increase <20% over baseline

### 1.2 Testing Scope

**In Scope:**
- AG-UI event generation and streaming
- MCP-to-AG-UI bridge functionality
- State management and synchronization
- Tool integration and streaming
- Error handling and recovery
- Performance and scalability
- Integration with existing MCP workflow

**Out of Scope:**
- Frontend AG-UI client implementation
- MCP server modifications
- External API testing (Google Search, OpenAI)
- Infrastructure deployment testing

## 2. Test Categories

### 2.1 Unit Testing

**Test Structure:**
```python
# tests/unit/test_ag_ui_events.py
import pytest
from unittest.mock import Mock, AsyncMock
from datetime import datetime
from agents.ag_ui_financial_agent import AGUIFinancialAgent
from utils.event_stream import FinancialAnalysisEventStream

class TestAGUIEventGeneration:
    """Unit tests for AG-UI event generation"""
    
    @pytest.fixture
    def mock_observer(self):
        """Mock observer for event testing"""
        return Mock()
    
    @pytest.fixture
    def mock_mcp_agent(self):
        """Mock MCP agent wrapper"""
        return Mock()
    
    @pytest.fixture
    def agui_agent(self, mock_mcp_agent):
        """AG-UI agent instance for testing"""
        return AGUIFinancialAgent(mock_mcp_agent, "test_agent")
    
    async def test_run_started_event_generation(self, agui_agent, mock_observer):
        """Test RUN_STARTED event is generated correctly"""
        # Setup
        input_data = RunAgentInput(
            threadId="test_thread",
            runId="test_run",
            messages=[{"role": "user", "content": "Test query"}]
        )
        
        # Execute
        await agui_agent._emit_run_started(input_data, mock_observer)
        
        # Verify
        mock_observer.next.assert_called_once()
        event = mock_observer.next.call_args[0][0]
        
        assert event["type"] == "RUN_STARTED"
        assert event["threadId"] == "test_thread"
        assert event["runId"] == "test_run"
        assert "timestamp" in event
        assert "metadata" in event
    
    async def test_text_message_streaming(self, agui_agent, mock_observer):
        """Test text message streaming generates correct event sequence"""
        # Setup
        test_content = "This is a test message for streaming validation"
        
        # Execute
        await agui_agent._emit_text_message(test_content, mock_observer, "test_msg_001")
        
        # Verify event sequence
        calls = mock_observer.next.call_args_list
        assert len(calls) >= 3  # START, CONTENT(s), END
        
        # Verify START event
        start_event = calls[0][0][0]
        assert start_event["type"] == "TEXT_MESSAGE_START"
        assert start_event["messageId"] == "test_msg_001"
        
        # Verify CONTENT events
        content_events = [call[0][0] for call in calls[1:-1]]
        for event in content_events:
            assert event["type"] == "TEXT_MESSAGE_CONTENT"
            assert event["messageId"] == "test_msg_001"
            assert "delta" in event
        
        # Verify END event
        end_event = calls[-1][0][0]
        assert end_event["type"] == "TEXT_MESSAGE_END"
        assert end_event["messageId"] == "test_msg_001"
    
    async def test_state_delta_generation(self, agui_agent, mock_observer):
        """Test state delta events generate correct JSON Patch operations"""
        # Setup
        input_data = RunAgentInput(threadId="test_thread", runId="test_run", messages=[])
        updates = {
            "progress_percentage": 50.0,
            "analysis_phase": "analysis",
            "current_operation": "financial_metrics_analysis"
        }
        
        # Execute
        await agui_agent._emit_state_update(updates, mock_observer, input_data)
        
        # Verify
        mock_observer.next.assert_called_once()
        event = mock_observer.next.call_args[0][0]
        
        assert event["type"] == "STATE_DELTA"
        assert event["threadId"] == "test_thread"
        assert len(event["delta"]) == 3
        
        # Verify JSON Patch operations
        operations = {op["path"]: op["value"] for op in event["delta"]}
        assert operations["/progress_percentage"] == 50.0
        assert operations["/analysis_phase"] == "analysis"
        assert operations["/current_operation"] == "financial_metrics_analysis"
    
    async def test_tool_call_event_sequence(self, agui_agent, mock_observer):
        """Test tool call events generate proper START/ARGS/END sequence"""
        # Setup
        input_data = RunAgentInput(threadId="test_thread", runId="test_run", messages=[])
        
        # Execute
        result = await agui_agent._execute_search_tool(
            "Apple Inc stock price", mock_observer, input_data
        )
        
        # Verify event sequence
        calls = mock_observer.next.call_args_list
        assert len(calls) == 3  # START, ARGS, END
        
        # Verify START event
        start_event = calls[0][0][0]
        assert start_event["type"] == "TOOL_CALL_START"
        assert start_event["toolCallName"] == "search_financial_data"
        
        # Verify ARGS event
        args_event = calls[1][0][0]
        assert args_event["type"] == "TOOL_CALL_ARGS"
        assert "delta" in args_event
        
        # Verify END event
        end_event = calls[2][0][0]
        assert end_event["type"] == "TOOL_CALL_END"
        assert "result" in end_event
        assert result is not None

class TestStateManager:
    """Unit tests for AG-UI state management"""
    
    @pytest.fixture
    def state_manager(self):
        """State manager instance for testing"""
        return AGUIStateManager()
    
    def test_state_initialization(self, state_manager):
        """Test state manager initializes with correct default state"""
        assert state_manager.current_state["analysis_phase"] == "initialized"
        assert state_manager.current_state["progress_percentage"] == 0.0
        assert state_manager.current_state["error_state"] is None
        assert len(state_manager.state_history) == 0
    
    def test_state_update_and_history(self, state_manager):
        """Test state updates are applied and history is maintained"""
        # Setup
        mock_observer = Mock()
        updates = {"progress_percentage": 25.0, "analysis_phase": "research"}
        
        # Execute
        state_manager.update_state(updates, mock_observer, "test_thread")
        
        # Verify state update
        assert state_manager.current_state["progress_percentage"] == 25.0
        assert state_manager.current_state["analysis_phase"] == "research"
        
        # Verify history
        assert len(state_manager.state_history) == 1
        assert state_manager.state_history[0]["progress_percentage"] == 0.0
        
        # Verify event emission
        mock_observer.next.assert_called_once()
        event = mock_observer.next.call_args[0][0]
        assert event["type"] == "STATE_DELTA"
    
    def test_json_patch_generation(self, state_manager):
        """Test JSON Patch operations are generated correctly"""
        # Setup
        old_state = {"field1": "value1", "field2": 10}
        new_state = {"field1": "updated_value1", "field2": 20, "field3": "new_value"}
        
        # Execute
        operations = state_manager._generate_json_patch(old_state, new_state)
        
        # Verify
        assert len(operations) == 3
        op_dict = {op["path"]: op for op in operations}
        
        assert op_dict["/field1"]["op"] == "replace"
        assert op_dict["/field1"]["value"] == "updated_value1"
        assert op_dict["/field2"]["value"] == 20
        assert op_dict["/field3"]["value"] == "new_value"

class TestToolBridge:
    """Unit tests for MCP-to-AG-UI tool bridge"""
    
    @pytest.fixture
    def tool_bridge(self):
        """Tool bridge instance for testing"""
        return MCPToAGUIToolBridge()
    
    async def test_tool_execution_with_events(self, tool_bridge):
        """Test MCP tool execution generates correct AG-UI events"""
        # Setup
        mock_observer = Mock()
        tool_args = {"query": "Apple Inc stock price", "search_type": "financial"}
        
        # Execute
        result = await tool_bridge.execute_mcp_tool_with_agui_events(
            "g-search", tool_args, mock_observer, "parent_msg_001"
        )
        
        # Verify event sequence
        calls = mock_observer.next.call_args_list
        assert len(calls) == 3  # START, ARGS, END
        
        # Verify tool mapping
        start_event = calls[0][0][0]
        assert start_event["toolCallName"] == "search_financial_data"
        
        # Verify result
        assert result is not None
        assert "Search results for:" in result
    
    async def test_tool_execution_error_handling(self, tool_bridge):
        """Test tool execution error handling and event generation"""
        # Setup
        mock_observer = Mock()
        
        # Mock tool execution to raise exception
        original_execute = tool_bridge._execute_mcp_tool
        async def mock_execute_error(*args, **kwargs):
            raise Exception("Mock tool execution error")
        tool_bridge._execute_mcp_tool = mock_execute_error
        
        # Execute and verify exception
        with pytest.raises(Exception, match="Mock tool execution error"):
            await tool_bridge.execute_mcp_tool_with_agui_events(
                "g-search", {}, mock_observer
            )
        
        # Verify error event was emitted
        calls = mock_observer.next.call_args_list
        end_event = calls[-1][0][0]  # Last event should be TOOL_CALL_END with error
        assert end_event["type"] == "TOOL_CALL_END"
        assert "error" in end_event
        
        # Restore original method
        tool_bridge._execute_mcp_tool = original_execute
```

### 2.2 Integration Testing

**Test Structure:**
```python
# tests/integration/test_full_workflow.py
import pytest
import asyncio
from unittest.mock import Mock, patch
from agents.ag_ui_research_agent import AGUIResearchAgent
from agents.ag_ui_analyst_agent import AGUIAnalystAgent
from agents.ag_ui_report_writer import AGUIReportWriter
from orchestrator.ag_ui_orchestrator import AGUIFinancialAnalysisOrchestrator

class TestFullWorkflowIntegration:
    """Integration tests for complete financial analysis workflow"""
    
    @pytest.fixture
    def orchestrator(self):
        """Orchestrator instance for integration testing"""
        return AGUIFinancialAnalysisOrchestrator(
            company_name="Apple Inc.",
            output_path="./test_reports/apple_test.md"
        )
    
    async def test_complete_financial_analysis_workflow(self, orchestrator):
        """Test complete end-to-end financial analysis with AG-UI events"""
        # Setup
        events_received = []
        
        def event_collector(event):
            events_received.append(event)
        
        input_data = RunAgentInput(
            threadId="integration_test_001",
            runId="workflow_test_001",
            messages=[{
                "role": "user",
                "content": "Analyze Apple Inc. stock performance and generate report"
            }]
        )
        
        # Execute
        workflow_observable = orchestrator.run_complete_analysis(input_data)()
        workflow_observable.subscribe(event_collector)
        
        # Wait for completion
        await asyncio.sleep(10)  # Allow workflow to complete
        
        # Verify workflow events
        assert len(events_received) > 0
        
        # Verify event sequence
        event_types = [event["type"] for event in events_received]
        assert "RUN_STARTED" in event_types
        assert "RUN_FINISHED" in event_types or "RUN_ERROR" in event_types
        
        # Verify agent execution events
        assert any("TOOL_CALL_START" in event_types for event in events_received)
        assert any("TEXT_MESSAGE_CONTENT" in event_types for event in events_received)
        assert any("STATE_DELTA" in event_types for event in events_received)
        
        # Verify state progression
        state_events = [e for e in events_received if e["type"] == "STATE_DELTA"]
        if state_events:
            # Check progress increases
            progress_values = []
            for event in state_events:
                for op in event.get("delta", []):
                    if op.get("path") == "/progress_percentage":
                        progress_values.append(op["value"])
            
            if progress_values:
                assert progress_values == sorted(progress_values)  # Progress should increase
    
    async def test_agent_coordination_and_state_sharing(self, orchestrator):
        """Test agents coordinate properly and share state"""
        # Setup
        state_snapshots = []
        
        def state_collector(event):
            if event["type"] in ["STATE_DELTA", "STATE_SNAPSHOT"]:
                state_snapshots.append(event)
        
        input_data = RunAgentInput(
            threadId="coordination_test_001",
            runId="coordination_run_001",
            messages=[{"role": "user", "content": "Test coordination"}]
        )
        
        # Execute
        workflow_observable = orchestrator.run_complete_analysis(input_data)()
        workflow_observable.subscribe(state_collector)
        
        await asyncio.sleep(8)
        
        # Verify state coordination
        assert len(state_snapshots) > 0
        
        # Verify state consistency across agents
        if len(state_snapshots) > 1:
            # Check that company_name is consistent
            for event in state_snapshots:
                if event["type"] == "STATE_DELTA":
                    for op in event.get("delta", []):
                        if op.get("path") == "/company_name":
                            assert op["value"] == "Apple Inc."
    
    async def test_error_recovery_integration(self, orchestrator):
        """Test error recovery across the full workflow"""
        # Setup - Mock a tool to fail initially then succeed
        error_events = []
        recovery_events = []
        
        def error_collector(event):
            if event["type"] == "RUN_ERROR":
                error_events.append(event)
            elif event["type"] == "RUN_FINISHED":
                recovery_events.append(event)
        
        # Mock tool bridge to simulate intermittent failures
        original_execute = orchestrator.tool_bridge.execute_mcp_tool_with_agui_events
        call_count = 0
        
        async def mock_execute_with_failure(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:  # Fail first call
                raise Exception("Simulated API failure")
            return await original_execute(*args, **kwargs)
        
        orchestrator.tool_bridge.execute_mcp_tool_with_agui_events = mock_execute_with_failure
        
        input_data = RunAgentInput(
            threadId="error_recovery_test_001",
            runId="error_recovery_run_001",
            messages=[{"role": "user", "content": "Test error recovery"}]
        )
        
        # Execute
        workflow_observable = orchestrator.run_complete_analysis(input_data)()
        workflow_observable.subscribe(error_collector)
        
        await asyncio.sleep(12)  # Allow time for retry logic
        
        # Verify error handling
        # Note: Depending on implementation, might have errors that are recovered from
        # The key is that the workflow should either complete successfully or fail gracefully
        
        # Restore original method
        orchestrator.tool_bridge.execute_mcp_tool_with_agui_events = original_execute

### 2.3 Performance Testing

**Test Structure:**
```python
# tests/performance/test_ag_ui_performance.py
import pytest
import asyncio
import time
import psutil
import statistics
from concurrent.futures import ThreadPoolExecutor
from agents.ag_ui_financial_agent import AGUIFinancialAgent

class TestAGUIPerformance:
    """Performance tests for AG-UI integration"""

    async def test_event_generation_latency(self):
        """Test event generation meets latency requirements (<10ms per event)"""
        # Setup
        agent = AGUIFinancialAgent(Mock(), "perf_test_agent")
        mock_observer = Mock()
        input_data = RunAgentInput(threadId="perf_test", runId="perf_run", messages=[])

        # Measure event generation latency
        latencies = []

        for i in range(100):
            start_time = time.perf_counter()
            await agent._emit_run_started(input_data, mock_observer)
            end_time = time.perf_counter()
            latencies.append((end_time - start_time) * 1000)  # Convert to ms

        # Verify performance requirements
        avg_latency = statistics.mean(latencies)
        p95_latency = statistics.quantiles(latencies, n=20)[18]  # 95th percentile

        assert avg_latency < 10.0, f"Average latency {avg_latency:.2f}ms exceeds 10ms requirement"
        assert p95_latency < 15.0, f"P95 latency {p95_latency:.2f}ms exceeds 15ms threshold"

    async def test_state_update_performance(self):
        """Test state update processing meets performance requirements (<50ms)"""
        # Setup
        state_manager = AGUIStateManager()
        mock_observer = Mock()

        # Measure state update latency
        latencies = []

        for i in range(50):
            updates = {
                "progress_percentage": float(i * 2),
                "analysis_phase": f"phase_{i}",
                "current_operation": f"operation_{i}"
            }

            start_time = time.perf_counter()
            state_manager.update_state(updates, mock_observer, "perf_test_thread")
            end_time = time.perf_counter()
            latencies.append((end_time - start_time) * 1000)

        # Verify performance requirements
        avg_latency = statistics.mean(latencies)
        max_latency = max(latencies)

        assert avg_latency < 25.0, f"Average state update latency {avg_latency:.2f}ms exceeds 25ms"
        assert max_latency < 50.0, f"Max state update latency {max_latency:.2f}ms exceeds 50ms requirement"

    async def test_concurrent_analysis_performance(self):
        """Test system handles concurrent analyses within performance limits"""
        # Setup
        orchestrator = AGUIFinancialAnalysisOrchestrator("Test Corp", "./test_output")

        # Measure memory usage before
        process = psutil.Process()
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Execute concurrent analyses
        concurrent_count = 5
        tasks = []

        for i in range(concurrent_count):
            input_data = RunAgentInput(
                threadId=f"concurrent_test_{i}",
                runId=f"concurrent_run_{i}",
                messages=[{"role": "user", "content": f"Analyze company {i}"}]
            )

            task = asyncio.create_task(self._run_analysis_with_timeout(orchestrator, input_data))
            tasks.append(task)

        # Wait for all analyses to complete
        start_time = time.perf_counter()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.perf_counter()

        # Measure memory usage after
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - baseline_memory

        # Verify performance requirements
        total_time = end_time - start_time
        assert total_time < 120.0, f"Concurrent analyses took {total_time:.2f}s, exceeds 120s limit"

        memory_increase_percent = (memory_increase / baseline_memory) * 100
        assert memory_increase_percent < 20.0, f"Memory increase {memory_increase_percent:.1f}% exceeds 20% limit"

        # Verify most analyses completed successfully
        successful_analyses = sum(1 for result in results if not isinstance(result, Exception))
        success_rate = successful_analyses / concurrent_count
        assert success_rate >= 0.8, f"Success rate {success_rate:.1f} below 80% threshold"

    async def _run_analysis_with_timeout(self, orchestrator, input_data, timeout=60):
        """Helper method to run analysis with timeout"""
        try:
            events = []

            def event_collector(event):
                events.append(event)

            workflow_observable = orchestrator.run_complete_analysis(input_data)()
            workflow_observable.subscribe(event_collector)

            # Wait for completion or timeout
            await asyncio.wait_for(asyncio.sleep(timeout), timeout=timeout)
            return events

        except asyncio.TimeoutError:
            return Exception("Analysis timed out")
        except Exception as e:
            return e

    async def test_event_streaming_throughput(self):
        """Test event streaming meets throughput requirements (100+ events/second)"""
        # Setup
        agent = AGUIFinancialAgent(Mock(), "throughput_test_agent")
        events_generated = []

        def event_collector(event):
            events_generated.append(event)

        # Generate events at high rate
        start_time = time.perf_counter()

        for i in range(500):  # Generate 500 events
            await agent._emit_text_message(f"Test message {i}", event_collector, f"msg_{i}")

        end_time = time.perf_counter()

        # Calculate throughput
        total_time = end_time - start_time
        events_per_second = len(events_generated) / total_time

        # Verify throughput requirement
        assert events_per_second >= 100.0, f"Event throughput {events_per_second:.1f} events/s below 100 requirement"

### 2.4 Load Testing

**Test Structure:**
```python
# tests/load/test_ag_ui_load.py
import pytest
import asyncio
import aiohttp
import time
from concurrent.futures import ThreadPoolExecutor

class TestAGUILoadTesting:
    """Load testing for AG-UI integration under stress conditions"""

    async def test_sustained_load_analysis(self):
        """Test system handles sustained load over extended period"""
        # Setup
        orchestrator = AGUIFinancialAnalysisOrchestrator("Load Test Corp", "./load_test_output")

        # Run sustained load for 10 minutes
        test_duration_seconds = 600  # 10 minutes
        analyses_per_minute = 6  # 1 every 10 seconds

        start_time = time.time()
        completed_analyses = 0
        failed_analyses = 0

        while (time.time() - start_time) < test_duration_seconds:
            try:
                input_data = RunAgentInput(
                    threadId=f"load_test_{completed_analyses}",
                    runId=f"load_run_{completed_analyses}",
                    messages=[{"role": "user", "content": "Load test analysis"}]
                )

                # Run analysis with timeout
                await asyncio.wait_for(
                    self._execute_single_analysis(orchestrator, input_data),
                    timeout=60.0
                )
                completed_analyses += 1

            except Exception as e:
                failed_analyses += 1
                print(f"Analysis failed: {e}")

            # Wait before next analysis
            await asyncio.sleep(10)

        # Verify load test results
        total_analyses = completed_analyses + failed_analyses
        success_rate = completed_analyses / total_analyses if total_analyses > 0 else 0

        assert success_rate >= 0.95, f"Success rate {success_rate:.2f} below 95% under sustained load"
        assert completed_analyses >= 50, f"Only {completed_analyses} analyses completed in 10 minutes"

    async def _execute_single_analysis(self, orchestrator, input_data):
        """Execute single analysis for load testing"""
        events = []

        def event_collector(event):
            events.append(event)

        workflow_observable = orchestrator.run_complete_analysis(input_data)()
        workflow_observable.subscribe(event_collector)

        # Wait for completion
        await asyncio.sleep(30)  # Allow time for analysis

        # Check for completion events
        event_types = [event["type"] for event in events]
        if "RUN_FINISHED" not in event_types and "RUN_ERROR" not in event_types:
            raise Exception("Analysis did not complete within timeout")

        return events

    async def test_burst_load_handling(self):
        """Test system handles sudden burst of concurrent requests"""
        # Setup
        orchestrator = AGUIFinancialAnalysisOrchestrator("Burst Test Corp", "./burst_test_output")

        # Create burst of 20 concurrent analyses
        burst_size = 20
        tasks = []

        for i in range(burst_size):
            input_data = RunAgentInput(
                threadId=f"burst_test_{i}",
                runId=f"burst_run_{i}",
                messages=[{"role": "user", "content": f"Burst analysis {i}"}]
            )

            task = asyncio.create_task(
                self._execute_single_analysis_with_timeout(orchestrator, input_data, 90)
            )
            tasks.append(task)

        # Execute burst
        start_time = time.perf_counter()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.perf_counter()

        # Analyze results
        successful_results = [r for r in results if not isinstance(r, Exception)]
        failed_results = [r for r in results if isinstance(r, Exception)]

        success_rate = len(successful_results) / burst_size
        total_time = end_time - start_time

        # Verify burst handling
        assert success_rate >= 0.8, f"Burst success rate {success_rate:.2f} below 80%"
        assert total_time < 180.0, f"Burst processing took {total_time:.1f}s, exceeds 180s limit"

        print(f"Burst test: {len(successful_results)}/{burst_size} successful in {total_time:.1f}s")

    async def _execute_single_analysis_with_timeout(self, orchestrator, input_data, timeout):
        """Execute analysis with specific timeout for load testing"""
        try:
            return await asyncio.wait_for(
                self._execute_single_analysis(orchestrator, input_data),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            return Exception(f"Analysis timed out after {timeout}s")

### 2.5 Compatibility Testing

**Test Structure:**
```python
# tests/compatibility/test_mcp_compatibility.py
import pytest
from unittest.mock import Mock, patch
from mcp_agent.examples.usecases.mcp_financial_analyzer.main import main as mcp_main
from orchestrator.ag_ui_orchestrator import AGUIFinancialAnalysisOrchestrator

class TestMCPCompatibility:
    """Test backward compatibility with existing MCP Financial Analyzer"""

    async def test_existing_mcp_workflow_unchanged(self):
        """Test existing MCP workflow continues to work without AG-UI"""
        # This test would run the original MCP workflow
        # and verify it produces the same results as before

        # Setup - Mock the original MCP components
        with patch('mcp_agent.examples.usecases.mcp_financial_analyzer.main.Orchestrator') as mock_orchestrator:
            # Configure mock to simulate original behavior
            mock_instance = Mock()
            mock_orchestrator.return_value = mock_instance
            mock_instance.run.return_value = "Original MCP analysis result"

            # Execute original workflow
            # result = mcp_main(["--company", "Apple Inc."])

            # Verify original functionality is preserved
            # assert "Original MCP analysis result" in str(result)
            mock_orchestrator.assert_called_once()

    async def test_ag_ui_wrapper_preserves_mcp_functionality(self):
        """Test AG-UI wrapper preserves all MCP agent functionality"""
        # Setup
        mock_mcp_agent = Mock()
        mock_mcp_agent.run.return_value = "MCP agent result"

        agui_agent = AGUIFinancialAgent(mock_mcp_agent, "compatibility_test")

        # Test that AG-UI wrapper can access MCP functionality
        assert agui_agent.mcp_agent == mock_mcp_agent

        # Test that MCP methods are still accessible
        result = agui_agent.mcp_agent.run()
        assert result == "MCP agent result"

    async def test_schema_compatibility(self):
        """Test AG-UI integration maintains MCP schema compatibility"""
        # Import original schemas
        from mcp_agent.examples.usecases.mcp_financial_analyzer.schemas.agent_schemas import (
            FinancialResearchInputSchema,
            FinancialAnalysisInputSchema,
            ReportGenerationInputSchema
        )

        # Test that schemas are still valid and usable
        research_schema = FinancialResearchInputSchema(
            company_name="Apple Inc.",
            research_depth="comprehensive"
        )

        analysis_schema = FinancialAnalysisInputSchema(
            company_name="Apple Inc.",
            research_data="Mock research data"
        )

        report_schema = ReportGenerationInputSchema(
            company_name="Apple Inc.",
            analysis_data="Mock analysis data",
            output_path="./test_report.md"
        )

        # Verify schemas are valid
        assert research_schema.company_name == "Apple Inc."
        assert analysis_schema.company_name == "Apple Inc."
        assert report_schema.output_path == "./test_report.md"

## 3. Test Execution Strategy

### 3.1 Test Environment Setup

**Development Environment:**
```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-mock pytest-cov aiohttp psutil

# Setup test configuration
export TEST_ENVIRONMENT=development
export AG_UI_TEST_MODE=true
export MCP_TEST_SERVERS=mock

# Run test database/mock services
docker-compose -f docker-compose.test.yml up -d
```

**CI/CD Pipeline Integration:**
```yaml
# .github/workflows/ag-ui-tests.yml
name: AG-UI Integration Tests

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install -r requirements-test.txt
      - name: Run unit tests
        run: pytest tests/unit/ -v --cov=agents --cov=utils

  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v2
      - name: Setup test environment
        run: docker-compose -f docker-compose.test.yml up -d
      - name: Run integration tests
        run: pytest tests/integration/ -v --timeout=300

  performance-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    steps:
      - uses: actions/checkout@v2
      - name: Run performance tests
        run: pytest tests/performance/ -v --timeout=600
```

### 3.2 Test Data Management

**Mock Data Setup:**
```python
# tests/fixtures/financial_data.py
MOCK_FINANCIAL_DATA = {
    "Apple Inc.": {
        "stock_price": "$185.23",
        "market_cap": "$2.89T",
        "pe_ratio": 28.5,
        "earnings": {
            "q1_2025": {"revenue": "$89.5B", "eps": "$1.52"},
            "q4_2024": {"revenue": "$85.2B", "eps": "$1.45"}
        },
        "news": [
            "Apple reports strong Q1 earnings",
            "New iPhone sales exceed expectations"
        ]
    }
}

@pytest.fixture
def mock_financial_data():
    return MOCK_FINANCIAL_DATA
```

### 3.3 Test Reporting and Metrics

**Test Metrics Collection:**
- Test execution time per category
- Code coverage percentage
- Performance benchmark results
- Error rate and failure analysis
- Resource utilization during tests

**Test Report Generation:**
```bash
# Generate comprehensive test report
pytest --html=reports/test_report.html --cov-report=html:reports/coverage
```

This comprehensive testing strategy ensures the AG-UI integration is thoroughly validated across all dimensions while maintaining backward compatibility with the existing MCP Financial Analyzer system.
```
