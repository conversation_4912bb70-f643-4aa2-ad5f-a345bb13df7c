# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Setup
```bash
poetry install && poetry shell
```

### Code Quality & Testing
```bash
# Run tests with coverage
poetry run pytest --cov=atomic_agents atomic-agents

# Format code
poetry run black atomic-agents atomic-assembler atomic-examples atomic-forge

# Lint code
poetry run flake8 --extend-exclude=.venv atomic-agents atomic-assembler atomic-examples atomic-forge
```

### Documentation
```bash
cd docs && make html
```

### CLI Usage
```bash
# Run the Atomic Assembler CLI
poetry run atomic
```

## Project Architecture

This is a monorepo containing a modular AI agent framework built around the concept of "atomicity" - small, reusable, composable components.

### Core Components

1. **atomic-agents/**: Core library with BaseAgent, tools, and components
   - `BaseAgent`: Main agent class with pluggable components
   - Schema-driven development using Pydantic for input/output validation
   - Memory system for conversation history
   - Context providers for dynamic prompt injection

2. **atomic-assembler/**: TUI-based CLI tool for managing components
   - Entry point: `atomic` command
   - Downloads and manages tools from Atomic Forge

3. **atomic-examples/**: Real-world examples and tutorials
   - RAG chatbot, web search agent, YouTube summarizer
   - Quickstart examples in `atomic-examples/quickstart/quickstart/`

4. **atomic-forge/**: Collection of reusable tools
   - Calculator, SearxNG search, YouTube transcript scraper
   - Each tool has input/output schemas and usage examples

### Key Architectural Patterns

- **Schema Alignment**: Agents' output schemas can match tools' input schemas for seamless chaining
- **Provider Agnostic**: Built on Instructor framework, supports OpenAI, Anthropic, Groq, Ollama, etc.
- **Component System**: Memory, prompt generators, context providers are pluggable
- **Tool System**: Standardized tool interface with factories

### Dependencies

- **Instructor**: Core LLM interaction framework
- **Pydantic**: Data validation and serialization (>=2.11.0)
- **Rich/Textual**: CLI and TUI interfaces
- **MCP**: Model Context Protocol support

## Development Notes

- Python >=3.10,<4.0
- Uses Poetry for dependency management
- Black formatter with line length 127
- Framework designed for production use with emphasis on modularity and predictability
- Tests are organized alongside source code