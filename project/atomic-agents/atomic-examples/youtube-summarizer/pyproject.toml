[tool.poetry]
name = "youtube-summarizer"
version = "1.0.0"
description = "Youtube Summarizer example for Atomic Agents"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
atomic-agents = {path = "../..", develop = true}
openai = ">=1.35.12,<2.0.0"
pydantic = ">=2.10.3,<3.0.0"
google-api-python-client = ">=2.101.0,<3.0.0"
youtube-transcript-api = ">=0.1.9,<1.0.0"
instructor = ">=1.5.2,<2.0.0"
python-dotenv = ">=1.0.1,<2.0.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
