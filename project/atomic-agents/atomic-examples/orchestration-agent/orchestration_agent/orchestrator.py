from typing import Union
import openai
from pydantic import Field
from atomic_agents.agents.base_agent import BaseAgent, BaseAgentConfig
from atomic_agents.lib.base.base_io_schema import BaseIOSchema
from atomic_agents.lib.components.agent_memory import AgentMemory
from atomic_agents.lib.components.system_prompt_generator import SystemPromptGenerator, SystemPromptContextProviderBase

from orchestration_agent.tools.searxng_search import (
    SearxNGSearchTool,
    SearxNGSearchToolConfig,
    SearxNGSearchToolInputSchema,
    SearxNGSearchToolOutputSchema,
)
from orchestration_agent.tools.calculator import (
    CalculatorTool,
    CalculatorToolConfig,
    CalculatorToolInputSchema,
    CalculatorToolOutputSchema,
)

import instructor
from datetime import datetime


########################
# INPUT/OUTPUT SCHEMAS #
########################
class OrchestratorInputSchema(BaseIOSchema):
    """Input schema for the Orchestrator Agent. Contains the user's message to be processed."""

    chat_message: str = Field(..., description="The user's input message to be analyzed and responded to.")


class OrchestratorOutputSchema(BaseIOSchema):
    """Combined output schema for the Orchestrator Agent. Contains the tool to use and its parameters."""

    tool: str = Field(..., description="The tool to use: 'search' or 'calculator'")
    tool_parameters: Union[SearxNGSearchToolInputSchema, CalculatorToolInputSchema] = Field(
        ..., description="The parameters for the selected tool"
    )


class FinalAnswerSchema(BaseIOSchema):
    """Schema for the final answer generated by the Orchestrator Agent."""

    final_answer: str = Field(..., description="The final answer generated based on the tool output and user query.")


#######################
# AGENT CONFIGURATION #
#######################
class OrchestratorAgentConfig(BaseAgentConfig):
    """Configuration for the Orchestrator Agent."""

    searxng_config: SearxNGSearchToolConfig
    calculator_config: CalculatorToolConfig


#####################
# CONTEXT PROVIDERS #
#####################
class CurrentDateProvider(SystemPromptContextProviderBase):
    def __init__(self, title):
        super().__init__(title)
        self.date = datetime.now().strftime("%Y-%m-%d")

    def get_info(self) -> str:
        return f"Current date in format YYYY-MM-DD: {self.date}"


######################
# ORCHESTRATOR AGENT #
######################
orchestrator_agent = BaseAgent(
    BaseAgentConfig(
        client=instructor.from_openai(openai.OpenAI()),
        model="gpt-4o-mini",
        system_prompt_generator=SystemPromptGenerator(
            background=[
                "You are an Orchestrator Agent that decides between using a search tool or a calculator tool based on user input.",
                "Use the search tool for queries requiring factual information, current events, or specific data.",
                "Use the calculator tool for mathematical calculations and expressions.",
            ],
            output_instructions=[
                "Analyze the input to determine whether it requires a web search or a calculation.",
                "For search queries, use the 'search' tool and provide 1-3 relevant search queries.",
                "For calculations, use the 'calculator' tool and provide the mathematical expression to evaluate.",
                "When uncertain, prefer using the search tool.",
                "Format the output using the appropriate schema.",
            ],
        ),
        input_schema=OrchestratorInputSchema,
        output_schema=OrchestratorOutputSchema,
    )
)

# Register the current date provider
orchestrator_agent.register_context_provider("current_date", CurrentDateProvider("Current Date"))


def execute_tool(
    searxng_tool: SearxNGSearchTool, calculator_tool: CalculatorTool, orchestrator_output: OrchestratorOutputSchema
) -> Union[SearxNGSearchToolOutputSchema, CalculatorToolOutputSchema]:
    if orchestrator_output.tool == "search":
        return searxng_tool.run(orchestrator_output.tool_parameters)
    elif orchestrator_output.tool == "calculator":
        return calculator_tool.run(orchestrator_output.tool_parameters)
    else:
        raise ValueError(f"Unknown tool: {orchestrator_output.tool}")


#################
# EXAMPLE USAGE #
#################
if __name__ == "__main__":
    import os
    from dotenv import load_dotenv
    from rich.console import Console
    from rich.panel import Panel
    from rich.syntax import Syntax

    load_dotenv()

    # Set up the OpenAI client
    client = instructor.from_openai(openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY")))

    # Initialize the tools
    searxng_tool = SearxNGSearchTool(SearxNGSearchToolConfig(base_url="http://localhost:8080", max_results=5))
    calculator_tool = CalculatorTool(CalculatorToolConfig())

    # Initialize Rich console
    console = Console()

    # Print the full system prompt
    console.print(Panel(orchestrator_agent.system_prompt_generator.generate_prompt(), title="System Prompt", expand=False))
    console.print("\n")

    # Example inputs
    inputs = [
        "Who won the Nobel Prize in Physics in 2024?",
        "Please calculate the sine of pi/3 to the third power",
    ]

    for user_input in inputs:
        console.print(Panel(f"[bold cyan]User Input:[/bold cyan] {user_input}", expand=False))

        # Create the input schema
        input_schema = OrchestratorInputSchema(chat_message=user_input)

        # Print the input schema
        console.print("\n[bold yellow]Generated Input Schema:[/bold yellow]")
        input_syntax = Syntax(str(input_schema.model_dump_json(indent=2)), "json", theme="monokai", line_numbers=True)
        console.print(input_syntax)

        # Run the orchestrator to get the tool selection and input
        orchestrator_output = orchestrator_agent.run(input_schema)

        # Print the orchestrator output
        console.print("\n[bold magenta]Orchestrator Output:[/bold magenta]")
        orchestrator_syntax = Syntax(
            str(orchestrator_output.model_dump_json(indent=2)), "json", theme="monokai", line_numbers=True
        )
        console.print(orchestrator_syntax)

        # Run the selected tool
        response = execute_tool(searxng_tool, calculator_tool, orchestrator_output)

        # Print the tool output
        console.print("\n[bold green]Tool Output:[/bold green]")
        output_syntax = Syntax(str(response.model_dump_json(indent=2)), "json", theme="monokai", line_numbers=True)
        console.print(output_syntax)

        console.print("\n" + "-" * 80 + "\n")

        orchestrator_agent.output_schema = FinalAnswerSchema
        orchestrator_agent.memory.add_message("system", response)
        final_answer = orchestrator_agent.run(input_schema)
        console.print(f"\n[bold blue]Final Answer:[/bold blue] {final_answer.final_answer}")
        orchestrator_agent.output_schema = OrchestratorOutputSchema

        # Reset the memory after each response
        orchestrator_agent.memory = AgentMemory()
