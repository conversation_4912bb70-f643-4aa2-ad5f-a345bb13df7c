# Example Projects

This section contains detailed examples of using Atomic Agents in various scenarios.

## Quickstart Examples

Simple examples to get started with the framework:

- Basic chatbot with memory
- Custom chatbot with personality
- Streaming responses
- Custom input/output schemas
- Multiple provider support

[View Quickstart Examples](https://github.com/BrainBlend-AI/atomic-agents/tree/main/atomic-examples/quickstart)

## Basic Multimodal

Examples of working with images and text:

- Image analysis with text descriptions
- Image-based question answering
- Visual content generation
- Multi-image comparisons

[View Basic Multimodal Examples](https://github.com/BrainBlend-AI/atomic-agents/tree/main/atomic-examples/basic-multimodal)

## RAG Chatbot

Build context-aware chatbots with retrieval-augmented generation:

- Document indexing and embedding
- Semantic search integration
- Context-aware responses
- Source attribution
- Follow-up suggestions

[View RAG Chatbot Examples](https://github.com/BrainBlend-AI/atomic-agents/tree/main/atomic-examples/rag-chatbot)

## Web Search Agent

Create agents that can search and analyze web content:

- Web search integration
- Content extraction
- Result synthesis
- Multi-source research
- Citation tracking

[View Web Search Examples](https://github.com/BrainBlend-AI/atomic-agents/tree/main/atomic-examples/web-search-agent)

## Deep Research

Perform comprehensive research tasks:

- Multi-step research workflows
- Information synthesis
- Source validation
- Structured output generation
- Citation management

[View Deep Research Examples](https://github.com/BrainBlend-AI/atomic-agents/tree/main/atomic-examples/deep-research)

## YouTube Summarizer

Extract and analyze information from videos:

- Transcript extraction
- Content summarization
- Key point identification
- Timestamp linking
- Chapter generation

[View YouTube Summarizer Examples](https://github.com/BrainBlend-AI/atomic-agents/tree/main/atomic-examples/youtube-summarizer)

## YouTube to Recipe

Convert cooking videos into structured recipes:

- Video analysis
- Recipe extraction
- Ingredient parsing
- Step-by-step instructions
- Time and temperature conversion

[View YouTube Recipe Examples](https://github.com/BrainBlend-AI/atomic-agents/tree/main/atomic-examples/youtube-to-recipe)

## Orchestration Agent

Coordinate multiple agents for complex tasks:

- Agent coordination
- Task decomposition
- Progress tracking
- Error handling
- Result aggregation

[View Orchestration Examples](https://github.com/BrainBlend-AI/atomic-agents/tree/main/atomic-examples/orchestration-agent)

## MCP Agent

Build intelligent agents using the Model Context Protocol:

- Server implementation with multiple transport methods
- Dynamic tool discovery and registration
- Natural language query processing
- Stateful conversation handling
- Extensible tool architecture

[View MCP Agent Documentation](mcp_agent.md)
[View MCP Agent Examples](https://github.com/BrainBlend-AI/atomic-agents/tree/main/atomic-examples/mcp-agent)
