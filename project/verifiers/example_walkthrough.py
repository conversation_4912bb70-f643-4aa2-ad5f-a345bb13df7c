#!/usr/bin/env python3
"""
Complete Example: Training a Math Problem Solver with GRPO

This example demonstrates the full pipeline of the verifiers framework:
1. Data preparation
2. Environment setup with custom rewards
3. Infrastructure setup (vLLM server)
4. GRPO training
5. Evaluation and comparison

The goal: Take a base model and make it better at solving grade school math problems
by learning from trial-and-error with automatic reward feedback.
"""

import verifiers as vf
from datasets import Dataset
from verifiers.utils.data_utils import extract_boxed_answer
import asyncio
import os

# =============================================================================
# STEP 1: PREPARE THE DATA
# =============================================================================

# Sample math problems with verifiable answers
math_problems = [
    {
        "question": "<PERSON> has 24 apples. She gives 8 apples to her friend and then buys 15 more apples. How many apples does <PERSON> have now?",
        "answer": "31"
    },
    {
        "question": "A rectangle has a length of 12 meters and a width of 8 meters. What is the area of the rectangle?",
        "answer": "96"
    },
    {
        "question": "Tom scored 85, 92, and 78 on his three math tests. What is his average score?",
        "answer": "85"
    },
    {
        "question": "A store sells pencils for $0.25 each. If Jake buys 8 pencils, how much does he pay in total?",
        "answer": "2.00"
    }
]

# Convert to HuggingFace dataset format
dataset = Dataset.from_list(math_problems)
print(f"📊 Dataset prepared with {len(dataset)} math problems")

# =============================================================================
# STEP 2: DEFINE THE LEARNING ENVIRONMENT
# =============================================================================

# System prompt that teaches the model the expected format
system_prompt = """You are a helpful math tutor. Solve the given math problem step by step.

Think through your reasoning inside <think>...</think> tags, showing your work clearly.
Then provide your final numerical answer inside \\boxed{...} tags.

Example:
<think>
The problem asks for 5 + 3.
I need to add these two numbers together.
5 + 3 = 8
</think>

\\boxed{8}
"""

# Parser to extract structured responses from the model
parser = vf.ThinkParser(extract_fn=extract_boxed_answer)

# =============================================================================
# STEP 3: DEFINE REWARD FUNCTIONS (THE LEARNING SIGNAL)
# =============================================================================

def correct_answer_reward(completion: str, answer: str, **kwargs) -> float:
    """
    Primary reward: 1.0 if the answer is exactly correct, 0.0 otherwise.
    This is what drives the learning - the model gets rewarded for correct answers.
    """
    try:
        predicted_answer = parser.parse_answer(completion) or ""
        # Clean up the answers for comparison
        predicted_clean = predicted_answer.strip()
        actual_clean = answer.strip()
        
        if predicted_clean == actual_clean:
            return 1.0  # Perfect score for correct answer
        else:
            return 0.0  # No reward for wrong answer
    except:
        return 0.0

def format_bonus_reward(completion: str, **kwargs) -> float:
    """
    Bonus reward: Small reward for following the correct format.
    This helps the model learn to structure its responses properly.
    """
    has_think_tags = "<think>" in completion and "</think>" in completion
    has_boxed_answer = "\\boxed{" in completion and "}" in completion
    
    if has_think_tags and has_boxed_answer:
        return 0.2  # Small bonus for proper format
    elif has_think_tags or has_boxed_answer:
        return 0.1  # Smaller bonus for partial format
    else:
        return 0.0  # No bonus for poor format

# Combine rewards with weights
rubric = vf.Rubric(
    funcs=[correct_answer_reward, format_bonus_reward],
    weights=[1.0, 0.2]  # Correctness is 5x more important than format
)

# =============================================================================
# STEP 4: CREATE THE TRAINING ENVIRONMENT
# =============================================================================

# The environment encapsulates the entire learning setup
vf_env = vf.SingleTurnEnv(
    dataset=dataset,
    system_prompt=system_prompt,
    parser=parser,
    rubric=rubric,
)

print("🏟️  Training environment created!")
print(f"   - Task type: Single-turn math problem solving")
print(f"   - Reward functions: {len(rubric.funcs)} (correctness + format)")
print(f"   - Expected format: <think>reasoning</think> \\boxed{answer}")

# =============================================================================
# STEP 5: EVALUATE BASELINE PERFORMANCE (BEFORE TRAINING)
# =============================================================================

async def evaluate_baseline():
    """Test how well the model performs BEFORE training"""
    from openai import OpenAI
    
    # Connect to vLLM server (assumes it's running on localhost:8000)
    client = OpenAI(base_url="http://localhost:8000/v1", api_key="dummy")
    
    print("\n🔍 Evaluating baseline performance...")
    try:
        results = await vf_env.evaluate_async(
            client=client,
            model="math-model",  # This should match your vLLM server model
            num_samples=len(dataset),
            temperature=0.7
        )
        
        avg_reward = sum(r['reward'] for r in results) / len(results)
        correct_answers = sum(1 for r in results if r['reward'] >= 0.8)
        
        print(f"   📈 Baseline Results:")
        print(f"   - Average reward: {avg_reward:.2f}")
        print(f"   - Correct answers: {correct_answers}/{len(results)} ({correct_answers/len(results)*100:.1f}%)")
        
        # Show a sample prediction
        sample = results[0]
        print(f"\n   📝 Sample baseline prediction:")
        print(f"   Question: {sample['prompt'][:100]}...")
        print(f"   Model answer: {parser.parse_answer(sample['completion']) or 'Failed to parse'}")
        print(f"   Correct answer: {sample['answer']}")
        print(f"   Reward: {sample['reward']:.2f}")
        
        return avg_reward
        
    except Exception as e:
        print(f"   ⚠️  Could not evaluate baseline: {e}")
        print(f"   (Make sure vLLM server is running: vf-vllm --model your-model)")
        return 0.0

# =============================================================================
# STEP 6: SETUP GRPO TRAINING
# =============================================================================

def setup_training():
    """Configure the GRPO trainer for math problem solving"""
    
    # Load the model for training
    model_name = "microsoft/DialoGPT-small"  # Small model for demo
    # In practice, you'd use: "Qwen/Qwen2.5-1.5B-Instruct" or similar
    
    print(f"\n🤖 Loading model: {model_name}")
    model, tokenizer = vf.get_model_and_tokenizer(model_name)
    
    # Configure training hyperparameters
    training_args = vf.grpo_defaults(run_name="math_solver_demo")
    
    # Customize for our small demo
    training_args.per_device_train_batch_size = 2  # Small batches for demo
    training_args.num_generations = 4              # 4 attempts per problem
    training_args.gradient_accumulation_steps = 2  # Effective batch size = 2*2 = 4
    training_args.max_steps = 20                   # Short training for demo
    training_args.learning_rate = 1e-5             # Reasonable learning rate
    training_args.logging_steps = 1                # Log every step
    training_args.save_steps = 10                  # Save checkpoint every 10 steps
    training_args.temperature = 0.8                # Some randomness for exploration
    training_args.max_prompt_length = 512          # Reasonable for math problems
    training_args.max_completion_length = 1024     # Allow detailed reasoning
    
    print(f"   📋 Training configuration:")
    print(f"   - Batch size: {training_args.per_device_train_batch_size}")
    print(f"   - Generations per prompt: {training_args.num_generations}")
    print(f"   - Total training steps: {training_args.max_steps}")
    print(f"   - Learning rate: {training_args.learning_rate}")
    
    # Create the trainer
    trainer = vf.GRPOTrainer(
        model=model,
        processing_class=tokenizer,
        env=vf_env,
        args=training_args,
        # Uncomment for LoRA training (memory efficient):
        # peft_config=vf.lora_defaults(r=8, alpha=16)
    )
    
    return trainer

# =============================================================================
# STEP 7: THE ACTUAL TRAINING PROCESS
# =============================================================================

def train_model():
    """Run the GRPO training process"""
    
    print("\n🚀 Starting GRPO training...")
    print("=" * 60)
    print("GRPO Training Process:")
    print("1. Generate multiple completions for each math problem")
    print("2. Evaluate each completion using our reward functions")
    print("3. Update model to increase probability of high-reward completions")
    print("4. Repeat until the model learns to solve problems correctly")
    print("=" * 60)
    
    trainer = setup_training()
    
    # This is where the magic happens!
    # The trainer will:
    # - Generate completions using the current model
    # - Score them with our rubric
    # - Update the model weights to prefer better completions
    # - Repeat this process for max_steps iterations
    
    try:
        trainer.train()
        print("\n✅ Training completed successfully!")
        return trainer
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        print("Common issues:")
        print("- vLLM server not running (run: vf-vllm --model your-model)")
        print("- GPU memory issues (reduce batch_size or use LoRA)")
        print("- Model compatibility issues")
        return None

# =============================================================================
# STEP 8: EVALUATE TRAINED MODEL
# =============================================================================

async def evaluate_trained_model(trainer):
    """Test how well the model performs AFTER training"""
    if trainer is None:
        print("❌ Cannot evaluate - training failed")
        return
    
    print("\n🔍 Evaluating trained model performance...")
    
    # Save the trained model
    trainer.save_model("./trained_math_solver")
    print("💾 Model saved to ./trained_math_solver")
    
    # TODO: Load the trained model and evaluate it
    # This would involve:
    # 1. Loading the saved model
    # 2. Running inference on test problems
    # 3. Comparing performance to baseline
    
    print("✅ Training pipeline completed!")
    print("\nTo use your trained model:")
    print("1. Load it with: model = AutoModelForCausalLM.from_pretrained('./trained_math_solver')")
    print("2. Use it to solve new math problems")
    print("3. The model should now be better at following the format and getting correct answers!")

# =============================================================================
# MAIN EXECUTION
# =============================================================================

async def main():
    """Run the complete training pipeline"""
    
    print("🎯 VERIFIERS FRAMEWORK DEMO: Training a Math Problem Solver")
    print("=" * 70)
    
    # Step 1: Evaluate baseline
    baseline_score = await evaluate_baseline()
    
    # Step 2: Train the model
    trainer = train_model()
    
    # Step 3: Evaluate trained model
    await evaluate_trained_model(trainer)
    
    print("\n🎉 Demo completed!")
    print("\nWhat just happened:")
    print("1. We defined a learning environment with math problems")
    print("2. We created reward functions that score correct answers")
    print("3. We used GRPO to train the model through trial and error")
    print("4. The model learned to solve problems better through reinforcement learning")
    print("\nThis is the core of how verifiers works: Environment + Rewards + GRPO = Better Models")

if __name__ == "__main__":
    # Note: This is a demo script showing the concepts
    # In practice, you'd run this with proper infrastructure:
    # 1. Start vLLM server: vf-vllm --model your-model
    # 2. Run training: python example_walkthrough.py
    
    print("🏃‍♂️ To run this demo:")
    print("1. Start vLLM server: vf-vllm --model microsoft/DialoGPT-small")
    print("2. Run: python example_walkthrough.py")
    print("3. Watch your model learn to solve math problems!")
    
    # asyncio.run(main())