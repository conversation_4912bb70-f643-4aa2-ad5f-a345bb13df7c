#!/usr/bin/env python3
"""
HOW VERIFIERS WORKS: The Core Mechanics Explained

This example breaks down EXACTLY how the verifiers framework works under the hood.
Think of this as the "engine diagram" of the framework.
"""

import verifiers as vf
from datasets import Dataset

# =============================================================================
# THE BIG PICTURE: What is GRPO and Why Does It Work?
# =============================================================================

"""
GRPO (Group-Relative Policy Optimization) is like teaching a student by:

1. GENERATE: Give the student the same problem multiple times
2. SCORE: Grade all their attempts 
3. LEARN: Make the student more likely to repeat the GOOD attempts
4. REPEAT: Do this over and over until they get really good

Traditional training teaches "copy this exact answer"
GRPO training teaches "figure out what makes answers good vs bad"

Here's the step-by-step breakdown:
"""

# =============================================================================
# STEP 1: THE DATASET (What We're Teaching)
# =============================================================================

simple_dataset = Dataset.from_list([
    {"question": "What is 2 + 3?", "answer": "5"},
    {"question": "What is 10 - 4?", "answer": "6"},
])

print("📚 DATASET: The problems we want the model to learn")
for i, example in enumerate(simple_dataset):
    print(f"   Problem {i+1}: {example['question']} → {example['answer']}")

# =============================================================================
# STEP 2: THE PARSER (How We Extract Answers)
# =============================================================================

def extract_number(text):
    """Extract a number from text like 'The answer is 5' → '5'"""
    import re
    matches = re.findall(r'\d+', text)
    return matches[0] if matches else None

parser = vf.ThinkParser(extract_fn=extract_number)

print("\n🔍 PARSER: How we extract answers from model output")
sample_outputs = [
    "I need to add 2 + 3. The answer is 5.",
    "Let me think... 10 minus 4 equals 6.",
    "This is tricky but I think it's 7.",  # Wrong answer
]

for output in sample_outputs:
    extracted = parser.parse_answer(output)
    print(f"   '{output}' → Extracted: '{extracted}'")

# =============================================================================
# STEP 3: THE REWARD FUNCTION (How We Score Attempts)
# =============================================================================

def correctness_reward(completion: str, answer: str, **kwargs) -> float:
    """
    This is THE CORE of learning!
    
    The model generates text, we extract the answer, and give a score.
    High scores = good behavior = model learns to repeat it
    Low scores = bad behavior = model learns to avoid it
    """
    predicted = parser.parse_answer(completion)
    
    if predicted == answer:
        return 1.0  # Perfect! Do this again!
    else:
        return 0.0  # Wrong! Don't do this!

print("\n🎯 REWARD FUNCTION: How we score the model's attempts")
print("   This is what drives the learning process!")

# Simulate different model outputs for "What is 2 + 3?"
test_completions = [
    "I think 2 + 3 = 5",        # Correct
    "2 + 3 is probably 6",      # Wrong  
    "The answer is 5",          # Correct
    "I don't know",             # Wrong (no number)
]

for completion in test_completions:
    reward = correctness_reward(completion, "5")
    print(f"   '{completion}' → Reward: {reward}")

# =============================================================================
# STEP 4: THE RUBRIC (Combining Multiple Rewards)
# =============================================================================

def format_reward(completion: str, **kwargs) -> float:
    """Bonus points for good format"""
    if "think" in completion.lower():
        return 0.2  # Small bonus for showing work
    return 0.0

# Combine multiple reward functions
rubric = vf.Rubric(
    funcs=[correctness_reward, format_reward],
    weights=[1.0, 0.2]  # Correctness is 5x more important
)

print("\n📊 RUBRIC: Combining multiple reward signals")
print("   Weight 1.0: Correctness (main goal)")
print("   Weight 0.2: Format bonus (secondary goal)")

test_completion = "Let me think... 2 + 3 = 5"
total_reward = rubric.score(test_completion, answer="5")
print(f"   '{test_completion}' → Total reward: {total_reward}")

# =============================================================================
# STEP 5: THE ENVIRONMENT (Putting It All Together)
# =============================================================================

env = vf.SingleTurnEnv(
    dataset=simple_dataset,
    system_prompt="Solve the math problem step by step.",
    parser=parser,
    rubric=rubric
)

print("\n🏟️ ENVIRONMENT: The complete learning setup")
print("   Dataset: Problems to solve")
print("   Parser: How to extract answers")
print("   Rubric: How to score attempts")
print("   System prompt: Instructions for the model")

# =============================================================================
# STEP 6: WHAT HAPPENS DURING TRAINING (The GRPO Process)
# =============================================================================

print("\n🚀 WHAT HAPPENS DURING TRAINING:")
print("=" * 50)

print("For each problem in the dataset:")
print("1. GENERATE: Model produces N different completions")
print("   Example problem: 'What is 2 + 3?'")
print("   Model generates 4 different attempts:")

fake_completions = [
    "I think 2 + 3 = 5",           # Good
    "2 + 3 is probably 6",         # Bad
    "Let me think... it's 5",      # Good + format bonus
    "The answer is 4",             # Bad
]

rewards = []
for i, completion in enumerate(fake_completions):
    reward = rubric.score(completion, answer="5")
    rewards.append(reward)
    print(f"   Attempt {i+1}: '{completion}' → Reward: {reward}")

print("\n2. RANK: Sort completions by reward")
sorted_pairs = sorted(zip(fake_completions, rewards), key=lambda x: x[1], reverse=True)
for i, (completion, reward) in enumerate(sorted_pairs):
    print(f"   Rank {i+1}: '{completion}' (reward: {reward})")

print("\n3. LEARN: Update model weights")
print("   - Increase probability of high-reward completions")
print("   - Decrease probability of low-reward completions")
print("   - This is done via gradient descent on the policy")

print("\n4. REPEAT: Do this for all problems, multiple times")
print("   - Each iteration, the model gets slightly better")
print("   - Eventually, it learns to consistently produce good answers")

# =============================================================================
# STEP 7: WHY THIS WORKS (The Theory)
# =============================================================================

print("\n🧠 WHY THIS WORKS:")
print("=" * 30)
print("• EXPLORATION: Model tries many different approaches")
print("• FEEDBACK: Reward function provides immediate, clear feedback")
print("• LEARNING: Model adjusts to prefer high-reward behaviors")
print("• GENERALIZATION: Model learns patterns that work across problems")
print("• ITERATION: Each round makes the model slightly better")

print("\n📈 COMPARISON TO OTHER APPROACHES:")
print("• Supervised Learning: 'Copy this exact answer'")
print("• GRPO: 'Figure out what makes answers good'")
print("• Result: Model learns to solve NEW problems, not just memorize")

# =============================================================================
# STEP 8: THE INFRASTRUCTURE (How It Scales)
# =============================================================================

print("\n⚙️ INFRASTRUCTURE FOR REAL TRAINING:")
print("=" * 40)
print("• vLLM Server: Fast parallel generation of completions")
print("• Async Processing: Score many completions simultaneously") 
print("• Multi-GPU: Distribute training across multiple GPUs")
print("• DeepSpeed: Memory-efficient training for large models")
print("• Checkpointing: Save progress and resume training")

print("\n🎯 TYPICAL TRAINING SETUP:")
print("   GPU 0-1: vLLM server (generation)")
print("   GPU 2-3: Training process (gradient updates)")
print("   Result: Continuous learning loop with high throughput")

# =============================================================================
# STEP 9: REAL-WORLD EXAMPLE
# =============================================================================

print("\n🌍 REAL-WORLD EXAMPLE:")
print("=" * 25)
print("Training a 7B model on 1000 math problems:")
print("• 8 GPUs total (4 for generation, 4 for training)")
print("• Generate 8 completions per problem")
print("• Score all completions with rubric")
print("• Update model weights based on scores")
print("• 500 training steps → significantly better math performance")
print("• Total time: ~2 hours")

print("\n✅ SUMMARY:")
print("Verifiers = Environment + Rewards + GRPO + Infrastructure")
print("Result: Models that learn to solve problems through trial and error")
print("Just like humans do!")

if __name__ == "__main__":
    print("\n🎓 This is how verifiers works under the hood!")
    print("The magic is in the combination of all these components working together.")