#!/usr/bin/env python3
"""
AG-UI Documentation Search Index
A Python script to search through the indexed AG-UI documentation.
"""

import json
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class SearchResult:
    """Represents a search result with relevance scoring."""
    title: str
    section: str
    description: str
    relevance_score: float
    source_url: str
    related_terms: List[str] = None

class AGUIDocSearcher:
    """Search engine for AG-UI documentation index."""
    
    def __init__(self, index_file: str = "ag-ui-docs-index.json"):
        """Initialize the searcher with the index file."""
        self.index_file = Path(index_file)
        self.index_data = self._load_index()
        
    def _load_index(self) -> Dict[str, Any]:
        """Load the JSON index file."""
        try:
            with open(self.index_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Index file {self.index_file} not found. Please ensure it exists.")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in index file: {e}")
    
    def search(self, query: str, max_results: int = 10) -> List[SearchResult]:
        """
        Search the documentation index for the given query.
        
        Args:
            query: Search query string
            max_results: Maximum number of results to return
            
        Returns:
            List of SearchResult objects sorted by relevance
        """
        query_lower = query.lower()
        results = []
        
        # Search in table of contents
        for section_id, section_data in self.index_data.get("table_of_contents", {}).items():
            score = self._calculate_relevance(query_lower, section_data)
            if score > 0:
                results.append(SearchResult(
                    title=section_data["title"],
                    section=section_id,
                    description=section_data["description"],
                    relevance_score=score,
                    source_url=section_data.get("source", ""),
                    related_terms=section_data.get("subsections", [])
                ))
        
        # Search in key concepts
        for concept_name, concept_data in self.index_data.get("key_concepts", {}).items():
            score = self._calculate_concept_relevance(query_lower, concept_name, concept_data)
            if score > 0:
                results.append(SearchResult(
                    title=f"Concept: {concept_name.title()}",
                    section="concept",
                    description=concept_data["definition"],
                    relevance_score=score,
                    source_url="",
                    related_terms=concept_data.get("related_sections", [])
                ))
        
        # Search in keyword index
        for keyword, keyword_data in self.index_data.get("keyword_index", {}).items():
            score = self._calculate_keyword_relevance(query_lower, keyword, keyword_data)
            if score > 0:
                results.append(SearchResult(
                    title=f"{keyword_data['type'].title()}: {keyword}",
                    section="keyword",
                    description=keyword_data["description"],
                    relevance_score=score,
                    source_url="",
                    related_terms=keyword_data.get("related_terms", [])
                ))
        
        # Sort by relevance score (descending) and return top results
        results.sort(key=lambda x: x.relevance_score, reverse=True)
        return results[:max_results]
    
    def _calculate_relevance(self, query: str, section_data: Dict[str, Any]) -> float:
        """Calculate relevance score for a section."""
        score = 0.0
        
        # Title match (highest weight)
        if query in section_data["title"].lower():
            score += 10.0
        
        # Description match
        if query in section_data["description"].lower():
            score += 5.0
        
        # Subsection matches
        for subsection in section_data.get("subsections", []):
            if query in subsection.lower():
                score += 2.0
        
        # Partial word matches
        words = query.split()
        for word in words:
            if len(word) > 2:  # Skip very short words
                if word in section_data["title"].lower():
                    score += 3.0
                if word in section_data["description"].lower():
                    score += 1.5
        
        return score
    
    def _calculate_concept_relevance(self, query: str, concept_name: str, concept_data: Dict[str, Any]) -> float:
        """Calculate relevance score for a concept."""
        score = 0.0
        
        # Concept name match
        if query in concept_name.lower():
            score += 8.0
        
        # Definition match
        if query in concept_data["definition"].lower():
            score += 4.0
        
        # Capabilities/types matches
        for item_list in ["capabilities", "types", "categories"]:
            for item in concept_data.get(item_list, []):
                if query in item.lower():
                    score += 2.0
        
        return score
    
    def _calculate_keyword_relevance(self, query: str, keyword: str, keyword_data: Dict[str, Any]) -> float:
        """Calculate relevance score for a keyword."""
        score = 0.0
        
        # Exact keyword match
        if query == keyword.lower():
            score += 15.0
        elif query in keyword.lower():
            score += 8.0
        
        # Description match
        if query in keyword_data["description"].lower():
            score += 4.0
        
        # Related terms match
        for term in keyword_data.get("related_terms", []):
            if query in term.lower():
                score += 2.0
        
        return score
    
    def get_section_details(self, section_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific section."""
        return self.index_data.get("table_of_contents", {}).get(section_id)
    
    def get_all_keywords(self) -> List[str]:
        """Get all available keywords in the index."""
        return list(self.index_data.get("keyword_index", {}).keys())
    
    def get_all_concepts(self) -> List[str]:
        """Get all available concepts in the index."""
        return list(self.index_data.get("key_concepts", {}).keys())

def main():
    """Command-line interface for searching the AG-UI documentation."""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python ag-ui-search-index.py <search_query>")
        print("Example: python ag-ui-search-index.py 'agent events'")
        return
    
    query = " ".join(sys.argv[1:])
    
    try:
        searcher = AGUIDocSearcher()
        results = searcher.search(query)
        
        if not results:
            print(f"No results found for: {query}")
            return
        
        print(f"Search results for: '{query}'\n")
        print("=" * 60)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.title}")
            print(f"   Section: {result.section}")
            print(f"   Description: {result.description}")
            print(f"   Relevance: {result.relevance_score:.1f}")
            if result.source_url:
                print(f"   Source: {result.source_url}")
            if result.related_terms:
                print(f"   Related: {', '.join(result.related_terms[:5])}")
            print()
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
