#!/usr/bin/env python3
import json
import sys
from datetime import datetime

def format_log():
    print('# Complete MCP Agent Log - Human Readable Format')
    print('**Original File:** /merge/mcp-agent/examples/usecases/mcp_financial_analyzer/logs/mcp-agent-20250625_131120.jsonl')
    print('**Date:** 2025-06-25 13:11:20')
    print('**Format:** All original content preserved in readable format')
    print()

    with open('/merge/mcp-agent/examples/usecases/mcp_financial_analyzer/logs/mcp-agent-20250625_131120.jsonl', 'r') as f:
        entries = []
        for line_num, line in enumerate(f, 1):
            try:
                entry = json.loads(line.strip())
                entry['_line_number'] = line_num
                entries.append(entry)
            except json.JSONDecodeError as e:
                print(f'**WARNING:** Line {line_num} could not be parsed: {e}')
                print(f'Raw content: {line.strip()}')
                print()

    print(f'## Complete Log Entries ({len(entries)} total)')
    print()

    for entry in entries:
        line_num = entry.get('_line_number', '?')
        level = entry.get('level', 'UNKNOWN')
        timestamp = entry.get('timestamp', 'N/A')
        namespace = entry.get('namespace', '')
        message = entry.get('message', 'No message')
        data = entry.get('data', None)
        
        # Format timestamp for readability
        try:
            if timestamp != 'N/A':
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                readable_time = dt.strftime('%H:%M:%S.%f')[:-3]  # Remove microseconds to milliseconds
            else:
                readable_time = 'N/A'
        except:
            readable_time = timestamp
        
        print(f'---')
        print(f'**Entry #{line_num}** | **{level}** | **{readable_time}**')
        
        if namespace:
            print(f'**Namespace:** `{namespace}`')
        
        print(f'**Message:** {message}')
        
        if data is not None:
            print(f'**Data:**')
            print('```json')
            print(json.dumps(data, indent=2, ensure_ascii=False))
            print('```')
        
        print()

if __name__ == "__main__":
    format_log()