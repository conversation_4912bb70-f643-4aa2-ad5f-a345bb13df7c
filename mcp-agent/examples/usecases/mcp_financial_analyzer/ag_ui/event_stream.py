"""
Financial Analysis Event Stream
===============================

Provides event streaming infrastructure for real-time communication
during financial analysis workflow execution.
"""

import asyncio
import json
from typing import Callable, List, Dict, Any, Optional
from datetime import datetime
from .events import (
    AGUIEvent,
    TextMessageStartEvent,
    TextMessageContentEvent,
    TextMessageEndEvent,
    generate_message_id
)


class EventObserver:
    """Simple event observer for collecting and forwarding events"""
    
    def __init__(self, callback: Optional[Callable] = None):
        """
        Initialize event observer.
        
        Args:
            callback: Optional callback function for events
        """
        self.events: List[Dict[str, Any]] = []
        self.callback = callback
        self.is_active = True
    
    def next(self, event: Dict[str, Any]):
        """
        Process next event.
        
        Args:
            event: Event data to process
        """
        if not self.is_active:
            return
            
        self.events.append(event)
        
        if self.callback:
            try:
                self.callback(event)
            except Exception as e:
                print(f"Error in event callback: {e}")
    
    def complete(self):
        """Mark observer as complete"""
        self.is_active = False
    
    def error(self, error: Exception):
        """Handle error in event stream"""
        print(f"Event stream error: {error}")
        self.is_active = False
    
    def get_events(self) -> List[Dict[str, Any]]:
        """Get all collected events"""
        return self.events.copy()


class FinancialAnalysisEventStream:
    """
    Event streaming infrastructure for financial analysis workflow.
    
    Provides methods for streaming text messages, tool calls, and state updates
    with proper chunking and timing for optimal user experience.
    """
    
    def __init__(self, chunk_size: int = 50, chunk_delay: float = 0.05):
        """
        Initialize event stream.
        
        Args:
            chunk_size: Size of text chunks for streaming
            chunk_delay: Delay between chunks in seconds
        """
        self.chunk_size = chunk_size
        self.chunk_delay = chunk_delay
        self.observers: List[EventObserver] = []
    
    def add_observer(self, observer: EventObserver):
        """Add event observer"""
        self.observers.append(observer)
    
    def remove_observer(self, observer: EventObserver):
        """Remove event observer"""
        if observer in self.observers:
            self.observers.remove(observer)
    
    def emit_event(self, event: AGUIEvent):
        """
        Emit event to all observers.
        
        Args:
            event: AG-UI event to emit
        """
        event_dict = event.dict()
        for observer in self.observers:
            observer.next(event_dict)
    
    async def stream_text_message(
        self,
        content: str,
        thread_id: str,
        role: str = "assistant",
        message_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Stream text message with proper chunking.
        
        Args:
            content: Text content to stream
            thread_id: Thread ID for events
            role: Message role (assistant, user, system)
            message_id: Optional message ID, generated if not provided
            metadata: Optional metadata for events
            
        Returns:
            Message ID used for streaming
        """
        if not message_id:
            message_id = generate_message_id()
        
        metadata = metadata or {}
        
        # Emit TEXT_MESSAGE_START event
        start_event = TextMessageStartEvent.create(
            thread_id=thread_id,
            message_id=message_id,
            role=role,
            **metadata
        )
        self.emit_event(start_event)
        
        # Stream content in chunks
        for i in range(0, len(content), self.chunk_size):
            chunk = content[i:i + self.chunk_size]
            
            content_event = TextMessageContentEvent.create(
                thread_id=thread_id,
                message_id=message_id,
                content_delta=chunk,
                **metadata
            )
            self.emit_event(content_event)
            
            # Add delay between chunks for realistic streaming
            if i + self.chunk_size < len(content):
                await asyncio.sleep(self.chunk_delay)
        
        # Emit TEXT_MESSAGE_END event
        end_event = TextMessageEndEvent.create(
            thread_id=thread_id,
            message_id=message_id,
            **metadata
        )
        self.emit_event(end_event)
        
        return message_id
    
    async def stream_analysis_phase(
        self,
        phase_name: str,
        phase_description: str,
        thread_id: str,
        progress: Optional[float] = None
    ) -> str:
        """
        Stream analysis phase information.
        
        Args:
            phase_name: Name of the analysis phase
            phase_description: Description of what's happening
            thread_id: Thread ID for events
            progress: Optional progress percentage
            
        Returns:
            Message ID for the phase update
        """
        progress_text = f" ({progress:.1f}%)" if progress is not None else ""
        content = f"🔄 **{phase_name}**{progress_text}\n\n{phase_description}"
        
        return await self.stream_text_message(
            content=content,
            thread_id=thread_id,
            metadata={
                "phase": phase_name,
                "progress": progress,
                "message_type": "phase_update"
            }
        )
    
    async def stream_agent_activity(
        self,
        agent_name: str,
        activity: str,
        thread_id: str,
        details: Optional[str] = None
    ) -> str:
        """
        Stream agent activity information.
        
        Args:
            agent_name: Name of the agent
            activity: Current activity description
            thread_id: Thread ID for events
            details: Optional additional details
            
        Returns:
            Message ID for the activity update
        """
        content = f"🤖 **{agent_name}**: {activity}"
        if details:
            content += f"\n\n{details}"
        
        return await self.stream_text_message(
            content=content,
            thread_id=thread_id,
            metadata={
                "agent_name": agent_name,
                "activity": activity,
                "message_type": "agent_activity"
            }
        )
    
    async def stream_tool_execution(
        self,
        tool_name: str,
        operation: str,
        thread_id: str,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Stream tool execution information.
        
        Args:
            tool_name: Name of the tool being executed
            operation: Description of the operation
            thread_id: Thread ID for events
            details: Optional operation details
            
        Returns:
            Message ID for the tool execution update
        """
        content = f"🔧 **{tool_name}**: {operation}"
        
        if details:
            if "query" in details:
                content += f"\n\nQuery: {details['query']}"
            if "url" in details:
                content += f"\n\nURL: {details['url']}"
        
        return await self.stream_text_message(
            content=content,
            thread_id=thread_id,
            metadata={
                "tool_name": tool_name,
                "operation": operation,
                "details": details,
                "message_type": "tool_execution"
            }
        )
    
    async def stream_results(
        self,
        results: Dict[str, Any],
        thread_id: str,
        result_type: str = "analysis_results"
    ) -> str:
        """
        Stream analysis results.
        
        Args:
            results: Results data to stream
            thread_id: Thread ID for events
            result_type: Type of results being streamed
            
        Returns:
            Message ID for the results
        """
        # Format results for display
        content = f"📊 **{result_type.replace('_', ' ').title()}**\n\n"
        
        if isinstance(results, dict):
            for key, value in results.items():
                if isinstance(value, (str, int, float)):
                    content += f"• **{key.replace('_', ' ').title()}**: {value}\n"
                elif isinstance(value, list) and len(value) > 0:
                    content += f"• **{key.replace('_', ' ').title()}**: {len(value)} items\n"
        else:
            content += str(results)
        
        return await self.stream_text_message(
            content=content,
            thread_id=thread_id,
            metadata={
                "result_type": result_type,
                "results": results,
                "message_type": "results"
            }
        )
    
    def complete_stream(self):
        """Complete all active streams"""
        for observer in self.observers:
            observer.complete()
    
    def get_stream_stats(self) -> Dict[str, Any]:
        """Get streaming statistics"""
        total_events = sum(len(obs.events) for obs in self.observers)
        active_observers = sum(1 for obs in self.observers if obs.is_active)
        
        return {
            "total_events": total_events,
            "active_observers": active_observers,
            "total_observers": len(self.observers),
            "chunk_size": self.chunk_size,
            "chunk_delay": self.chunk_delay
        }
