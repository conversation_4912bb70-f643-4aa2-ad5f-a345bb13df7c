#!/usr/bin/env python3
"""
Test script to demonstrate the AG-UI documentation search functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the search functionality directly
exec(open('ag-ui-search-index.py').read())

def test_search_functionality():
    """Test various search queries to demonstrate the index capabilities."""
    
    print("=" * 60)
    print("AG-UI Documentation Search Index - Test Suite")
    print("=" * 60)
    print()
    
    try:
        searcher = AGUIDocSearcher()
        
        # Test queries
        test_queries = [
            "agent",
            "events", 
            "AbstractAgent",
            "tool calls",
            "state management",
            "HttpAgent",
            "TEXT_MESSAGE_START",
            "python sdk",
            "debugging"
        ]
        
        for query in test_queries:
            print(f"🔍 Searching for: '{query}'")
            print("-" * 40)
            
            results = searcher.search(query, max_results=3)
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"{i}. {result.title}")
                    print(f"   📝 {result.description[:100]}...")
                    print(f"   📊 Relevance: {result.relevance_score:.1f}")
                    if result.source_url:
                        print(f"   🔗 {result.source_url}")
                    print()
            else:
                print("   No results found.")
                print()
            
            print()
        
        # Test additional functionality
        print("📚 Available Keywords:")
        keywords = searcher.get_all_keywords()
        print(f"   {', '.join(keywords[:10])}...")
        print()
        
        print("🧠 Available Concepts:")
        concepts = searcher.get_all_concepts()
        print(f"   {', '.join(concepts)}")
        print()
        
        # Test section details
        print("📖 Section 1 Details:")
        section_details = searcher.get_section_details("1")
        if section_details:
            print(f"   Title: {section_details['title']}")
            print(f"   Description: {section_details['description']}")
            print(f"   Subsections: {len(section_details.get('subsections', []))}")
        print()
        
        print("✅ Search index test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False
    
    return True

def interactive_search():
    """Interactive search mode for user queries."""
    
    print("🔍 Interactive AG-UI Documentation Search")
    print("Type your search queries (or 'quit' to exit)")
    print("-" * 50)
    
    try:
        searcher = AGUIDocSearcher()
        
        while True:
            query = input("\nSearch query: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            if not query:
                continue
            
            results = searcher.search(query, max_results=5)
            
            if results:
                print(f"\n📋 Found {len(results)} results for '{query}':")
                print("-" * 40)
                
                for i, result in enumerate(results, 1):
                    print(f"{i}. {result.title}")
                    print(f"   {result.description}")
                    print(f"   Relevance: {result.relevance_score:.1f}")
                    if result.source_url:
                        print(f"   Source: {result.source_url}")
                    if result.related_terms:
                        print(f"   Related: {', '.join(result.related_terms[:3])}")
                    print()
            else:
                print(f"\n❌ No results found for '{query}'")
                print("Try searching for: agent, events, tools, state, sdk")
    
    except KeyboardInterrupt:
        print("\n\n👋 Search session ended.")
    except Exception as e:
        print(f"\n❌ Error: {e}")

def main():
    """Main function to run tests or interactive mode."""
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            test_search_functionality()
        elif sys.argv[1] == "interactive":
            interactive_search()
        else:
            # Direct search query
            query = " ".join(sys.argv[1:])
            try:
                searcher = AGUIDocSearcher()
                results = searcher.search(query)
                
                if results:
                    print(f"Search results for: '{query}'\n")
                    for i, result in enumerate(results, 1):
                        print(f"{i}. {result.title}")
                        print(f"   {result.description}")
                        print(f"   Relevance: {result.relevance_score:.1f}")
                        if result.source_url:
                            print(f"   Source: {result.source_url}")
                        print()
                else:
                    print(f"No results found for: '{query}'")
            except Exception as e:
                print(f"Error: {e}")
    else:
        print("AG-UI Documentation Search Index")
        print("Usage:")
        print("  python test-search.py test           # Run test suite")
        print("  python test-search.py interactive    # Interactive mode")
        print("  python test-search.py <query>       # Direct search")
        print()
        print("Examples:")
        print("  python test-search.py agent")
        print("  python test-search.py 'tool calls'")
        print("  python test-search.py AbstractAgent")

if __name__ == "__main__":
    main()
