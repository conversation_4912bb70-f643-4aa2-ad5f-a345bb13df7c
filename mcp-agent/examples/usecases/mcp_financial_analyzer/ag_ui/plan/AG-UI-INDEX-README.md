# AG-UI Documentation Search Index

This directory contains a comprehensive searchable index of the Agent User Interaction Protocol (AG-UI) documentation, fetched from https://docs.ag-ui.com/llms-full.txt.

## Files Overview

### Core Index Files
- **`ag-ui-docs-index.json`** - Main structured index containing:
  - Document metadata and table of contents
  - Key concepts and their definitions
  - Comprehensive keyword mappings
  - Cross-references between sections

- **`ag-ui-search-index.py`** - Python search engine that provides:
  - Full-text search capabilities
  - Relevance scoring
  - Command-line interface
  - Programmatic API for integration

- **`ag-ui-content-full.txt`** - Complete original document content for reference

## Quick Start

### Using the Python Search Tool

```bash
# Search for specific terms
python ag-ui-search-index.py "agent events"
python ag-ui-search-index.py "tool calls"
python ag-ui-search-index.py "state management"

# Search for implementation details
python ag-ui-search-index.py "HttpAgent"
python ag-ui-search-index.py "AbstractAgent"
```

### Using the JSON Index Directly

```python
import json

# Load the index
with open('ag-ui-docs-index.json', 'r') as f:
    index = json.load(f)

# Browse sections
for section_id, section in index['table_of_contents'].items():
    print(f"{section_id}: {section['title']}")

# Explore key concepts
for concept, details in index['key_concepts'].items():
    print(f"{concept}: {details['definition']}")
```

## Index Structure

### Table of Contents
The index organizes content into 12 main sections:
1. **Agents** - Core components and implementation
2. **Core Architecture** - System design and communication
3. **Events** - Event-driven communication system
4. **JavaScript SDK** - Core types, encoder, and protocol buffers
5. **Python SDK** - Events, types, and encoder
6. **Tutorials** - Cursor development and debugging guides

### Key Concepts
Major concepts are indexed with:
- Clear definitions
- Related capabilities and types
- Cross-references to relevant sections
- Implementation details

### Keyword Index
Over 15 key terms are indexed including:
- **Classes**: `AbstractAgent`, `HttpAgent`, `BaseEvent`
- **Events**: `TEXT_MESSAGE_START`, `TOOL_CALL_START`, `RUN_STARTED`
- **Interfaces**: `RunAgentInput`, `EventType`
- **State Management**: `STATE_SNAPSHOT`, `STATE_DELTA`

## Search Features

### Relevance Scoring
The search engine uses weighted scoring:
- **Title matches**: 10.0 points
- **Exact keyword matches**: 15.0 points
- **Description matches**: 4.0-5.0 points
- **Related term matches**: 2.0 points
- **Partial word matches**: 1.5-3.0 points

### Search Types
1. **Section Search** - Find content by section titles and descriptions
2. **Concept Search** - Locate key concepts and their definitions
3. **Keyword Search** - Find specific classes, events, and interfaces
4. **Cross-Reference Search** - Discover related content

## Common Search Queries

### Getting Started
- `"what is agent"` - Learn about AG-UI agents
- `"architecture"` - Understand system design
- `"events"` - Explore event-driven communication

### Implementation
- `"AbstractAgent"` - Base agent class details
- `"HttpAgent"` - HTTP client implementation
- `"tool calls"` - Tool usage patterns

### SDK Usage
- `"python sdk"` - Python-specific documentation
- `"javascript sdk"` - JavaScript-specific documentation
- `"encoder"` - Event encoding utilities

### Debugging
- `"debugging"` - Troubleshooting guides
- `"dojo"` - AG-UI Dojo testing framework
- `"cursor"` - IDE integration

## Integration Examples

### Python Integration
```python
from ag_ui_search_index import AGUIDocSearcher

searcher = AGUIDocSearcher()
results = searcher.search("agent implementation")

for result in results:
    print(f"{result.title}: {result.description}")
```

### Command Line Usage
```bash
# Find all agent-related content
python ag-ui-search-index.py agent

# Search for specific event types
python ag-ui-search-index.py "TEXT_MESSAGE"

# Look up implementation patterns
python ag-ui-search-index.py "streaming events"
```

## Document Coverage

The index covers the complete AG-UI documentation including:
- **15 major sections** with detailed subsection mapping
- **5 key concepts** with comprehensive definitions
- **15+ indexed keywords** with cross-references
- **Complete event catalog** with lifecycle documentation
- **SDK references** for both Python and JavaScript
- **Tutorial content** for development and debugging

## Maintenance

To update the index:
1. Fetch the latest documentation from https://docs.ag-ui.com/llms-full.txt
2. Update `ag-ui-docs-index.json` with new sections or concepts
3. Add new keywords to the keyword index
4. Test search functionality with `ag-ui-search-index.py`

## License

This index is created for documentation purposes. Please refer to the original AG-UI documentation for licensing terms.
