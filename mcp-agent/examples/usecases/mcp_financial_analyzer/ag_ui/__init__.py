"""
AG-UI Integration Package for MCP Financial Analyzer
==================================================

This package provides AG-UI protocol support for the MCP Financial Analyzer,
enabling real-time streaming communication between the backend analysis workflow
and frontend applications.

Key Components:
- Event streaming infrastructure
- State management with JSON Patch support
- Tool bridge for MCP-to-AG-UI integration
- Agent wrappers with streaming capabilities
"""

from .events import (
    AGUIEvent,
    RunStartedEvent,
    RunFinishedEvent,
    RunErrorEvent,
    TextMessageStartEvent,
    TextMessageContentEvent,
    TextMessageEndEvent,
    ToolCallStartEvent,
    ToolCallArgsEvent,
    ToolCallEndEvent,
    StateSnapshotEvent,
    StateDeltaEvent,
)

from .state_manager import AGUIStateManager
from .tool_bridge import MCPToAGUIToolBridge
from .event_stream import FinancialAnalysisEventStream
from .abstract_agent import AbstractAgent

__all__ = [
    # Events
    "AGUIEvent",
    "RunStartedEvent", 
    "RunFinishedEvent",
    "RunErrorEvent",
    "TextMessageStartEvent",
    "TextMessageContentEvent", 
    "TextMessageEndEvent",
    "ToolCallStartEvent",
    "ToolCallArgsEvent",
    "ToolCallEndEvent",
    "StateSnapshotEvent",
    "StateDeltaEvent",
    
    # Core Components
    "AGUIStateManager",
    "MCPToAGUIToolBridge", 
    "FinancialAnalysisEventStream",
    "AbstractAgent",
]

__version__ = "1.0.0"
