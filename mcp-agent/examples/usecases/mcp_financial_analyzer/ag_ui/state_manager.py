"""
AG-UI State Manager
==================

Manages financial analysis workflow state with real-time synchronization
using JSON Patch operations for efficient state updates.
"""

import json
import copy
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from .events import StateDeltaEvent, StateSnapshotEvent


class AGUIStateManager:
    """
    Manages AG-UI state with JSON Patch-based delta updates.
    
    Provides real-time state synchronization for financial analysis workflow,
    tracking progress, current operations, and analysis results.
    """
    
    def __init__(self, initial_state: Optional[Dict[str, Any]] = None):
        """
        Initialize state manager with default financial analysis state.
        
        Args:
            initial_state: Optional initial state, uses default if not provided
        """
        self.current_state = initial_state or self._get_default_state()
        self.state_history: List[Dict[str, Any]] = []
        self.observers: List[Callable] = []
        
    def _get_default_state(self) -> Dict[str, Any]:
        """Get default state for financial analysis workflow"""
        return {
            "analysis_phase": "initialized",
            "progress_percentage": 0.0,
            "company_name": "",
            "current_operation": "initialization",
            "research_data": {},
            "analysis_results": {},
            "report_status": "not_started",
            "error_state": None,
            "start_time": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "agents_status": {
                "research_agent": "idle",
                "analyst_agent": "idle", 
                "report_writer": "idle"
            },
            "tool_calls": [],
            "messages": []
        }
    
    def add_observer(self, observer: Callable):
        """
        Add an observer function to be called on state changes.
        
        Args:
            observer: Function to call with state change events
        """
        self.observers.append(observer)
    
    def remove_observer(self, observer: Callable):
        """Remove an observer function"""
        if observer in self.observers:
            self.observers.remove(observer)
    
    def update_state(self, updates: Dict[str, Any], thread_id: str, 
                    emit_event: bool = True) -> List[Dict[str, Any]]:
        """
        Update state with new values and generate JSON Patch operations.
        
        Args:
            updates: Dictionary of state updates
            thread_id: Thread ID for event emission
            emit_event: Whether to emit STATE_DELTA event
            
        Returns:
            List of JSON Patch operations
        """
        # Store previous state for history
        previous_state = copy.deepcopy(self.current_state)
        self.state_history.append(previous_state)
        
        # Apply updates
        for key, value in updates.items():
            self.current_state[key] = value
        
        # Update timestamp
        self.current_state["last_updated"] = datetime.now().isoformat()
        
        # Generate JSON Patch operations
        patch_operations = self._generate_json_patch(previous_state, self.current_state)
        
        # Emit state delta event
        if emit_event and patch_operations:
            event = StateDeltaEvent.create(thread_id, patch_operations)
            self._notify_observers(event)
        
        return patch_operations
    
    def set_analysis_phase(self, phase: str, thread_id: str, progress: Optional[float] = None):
        """
        Update analysis phase with optional progress.
        
        Args:
            phase: New analysis phase
            thread_id: Thread ID for event emission
            progress: Optional progress percentage
        """
        updates = {"analysis_phase": phase}
        if progress is not None:
            updates["progress_percentage"] = progress
        
        self.update_state(updates, thread_id)
    
    def set_agent_status(self, agent_name: str, status: str, thread_id: str):
        """
        Update individual agent status.
        
        Args:
            agent_name: Name of the agent
            status: New status (idle, running, completed, error)
            thread_id: Thread ID for event emission
        """
        if "agents_status" not in self.current_state:
            self.current_state["agents_status"] = {}
        
        updates = {
            f"agents_status": {
                **self.current_state["agents_status"],
                agent_name: status
            }
        }
        self.update_state(updates, thread_id)
    
    def add_tool_call(self, tool_call_info: Dict[str, Any], thread_id: str):
        """
        Add tool call information to state.
        
        Args:
            tool_call_info: Tool call details
            thread_id: Thread ID for event emission
        """
        tool_calls = self.current_state.get("tool_calls", [])
        tool_calls.append({
            **tool_call_info,
            "timestamp": datetime.now().isoformat()
        })
        
        self.update_state({"tool_calls": tool_calls}, thread_id)
    
    def set_error_state(self, error_info: Dict[str, Any], thread_id: str):
        """
        Set error state information.
        
        Args:
            error_info: Error details
            thread_id: Thread ID for event emission
        """
        self.update_state({
            "error_state": error_info,
            "last_updated": datetime.now().isoformat()
        }, thread_id)
    
    def clear_error_state(self, thread_id: str):
        """Clear error state"""
        self.update_state({"error_state": None}, thread_id)
    
    def get_state_snapshot(self, thread_id: str, emit_event: bool = True) -> Dict[str, Any]:
        """
        Get complete state snapshot.
        
        Args:
            thread_id: Thread ID for event emission
            emit_event: Whether to emit STATE_SNAPSHOT event
            
        Returns:
            Complete current state
        """
        if emit_event:
            event = StateSnapshotEvent.create(thread_id, self.current_state)
            self._notify_observers(event)
        
        return copy.deepcopy(self.current_state)
    
    def _generate_json_patch(self, old_state: Dict[str, Any], 
                           new_state: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate JSON Patch operations between two states.
        
        Args:
            old_state: Previous state
            new_state: Current state
            
        Returns:
            List of JSON Patch operations
        """
        operations = []
        
        # Find changes
        for key, new_value in new_state.items():
            if key not in old_state:
                # Add operation
                operations.append({
                    "op": "add",
                    "path": f"/{key}",
                    "value": new_value
                })
            elif old_state[key] != new_value:
                # Replace operation
                operations.append({
                    "op": "replace", 
                    "path": f"/{key}",
                    "value": new_value
                })
        
        # Find removed keys
        for key in old_state:
            if key not in new_state:
                operations.append({
                    "op": "remove",
                    "path": f"/{key}"
                })
        
        return operations
    
    def _notify_observers(self, event):
        """Notify all observers of state change event"""
        for observer in self.observers:
            try:
                observer(event)
            except Exception as e:
                print(f"Error notifying observer: {e}")
    
    def get_progress_info(self) -> Dict[str, Any]:
        """Get current progress information"""
        return {
            "phase": self.current_state.get("analysis_phase", "unknown"),
            "progress": self.current_state.get("progress_percentage", 0.0),
            "operation": self.current_state.get("current_operation", "unknown"),
            "agents_status": self.current_state.get("agents_status", {}),
            "error_state": self.current_state.get("error_state")
        }
    
    def reset_state(self, thread_id: str):
        """Reset state to initial values"""
        self.current_state = self._get_default_state()
        self.state_history.clear()
        
        # Emit snapshot of reset state
        event = StateSnapshotEvent.create(thread_id, self.current_state)
        self._notify_observers(event)
