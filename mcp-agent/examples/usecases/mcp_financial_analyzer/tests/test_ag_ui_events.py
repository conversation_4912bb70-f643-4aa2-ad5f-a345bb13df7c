"""
Unit Tests for AG-UI Event Generation
====================================

Tests for AG-UI event creation, validation, and streaming functionality.
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, AsyncMock

from ag_ui.events import (
    RunStartedEvent,
    RunFinishedEvent,
    RunErrorEvent,
    TextMessageStartEvent,
    TextMessageContentEvent,
    TextMessageEndEvent,
    ToolCallStartEvent,
    ToolCallArgsEvent,
    ToolCallEndEvent,
    StateSnapshotEvent,
    StateDeltaEvent,
    create_event,
    generate_message_id,
    generate_tool_call_id
)


class TestAGUIEventGeneration:
    """Unit tests for AG-UI event generation"""
    
    def test_run_started_event_creation(self):
        """Test RUN_STARTED event is created correctly"""
        # Setup
        thread_id = "test_thread_001"
        run_id = "test_run_001"
        input_data = {"company_name": "Apple Inc.", "analysis_type": "comprehensive"}
        
        # Execute
        event = RunStartedEvent.create(thread_id, run_id, input_data, test_metadata="test_value")
        
        # Verify
        assert event.type == "RUN_STARTED"
        assert event.threadId == thread_id
        assert event.runId == run_id
        assert event.input == input_data
        assert event.metadata["test_metadata"] == "test_value"
        assert isinstance(event.timestamp, datetime)
    
    def test_run_finished_event_creation(self):
        """Test RUN_FINISHED event is created correctly"""
        # Setup
        thread_id = "test_thread_002"
        run_id = "test_run_002"
        output_data = {"report_path": "/path/to/report.md", "status": "completed"}
        
        # Execute
        event = RunFinishedEvent.create(thread_id, run_id, output_data)
        
        # Verify
        assert event.type == "RUN_FINISHED"
        assert event.threadId == thread_id
        assert event.runId == run_id
        assert event.output == output_data
        assert isinstance(event.timestamp, datetime)
    
    def test_run_error_event_creation(self):
        """Test RUN_ERROR event is created correctly"""
        # Setup
        thread_id = "test_thread_003"
        run_id = "test_run_003"
        error_info = {"type": "ValueError", "message": "Invalid company name"}
        
        # Execute
        event = RunErrorEvent.create(thread_id, run_id, error_info)
        
        # Verify
        assert event.type == "RUN_ERROR"
        assert event.threadId == thread_id
        assert event.runId == run_id
        assert event.error == error_info
        assert isinstance(event.timestamp, datetime)
    
    def test_text_message_event_sequence(self):
        """Test text message event sequence (START, CONTENT, END)"""
        # Setup
        thread_id = "test_thread_004"
        message_id = "test_msg_001"
        content_delta = "This is test content"
        
        # Execute
        start_event = TextMessageStartEvent.create(thread_id, message_id)
        content_event = TextMessageContentEvent.create(thread_id, message_id, content_delta)
        end_event = TextMessageEndEvent.create(thread_id, message_id)
        
        # Verify START event
        assert start_event.type == "TEXT_MESSAGE_START"
        assert start_event.threadId == thread_id
        assert start_event.messageId == message_id
        assert start_event.role == "assistant"
        
        # Verify CONTENT event
        assert content_event.type == "TEXT_MESSAGE_CONTENT"
        assert content_event.threadId == thread_id
        assert content_event.messageId == message_id
        assert content_event.delta == content_delta
        
        # Verify END event
        assert end_event.type == "TEXT_MESSAGE_END"
        assert end_event.threadId == thread_id
        assert end_event.messageId == message_id
    
    def test_tool_call_event_sequence(self):
        """Test tool call event sequence (START, ARGS, END)"""
        # Setup
        thread_id = "test_thread_005"
        tool_call_id = "test_tool_001"
        tool_name = "search_financial_data"
        args_delta = '{"query": "Apple stock price"}'
        result_data = {"success": True, "data": "Mock search results"}
        
        # Execute
        start_event = ToolCallStartEvent.create(thread_id, tool_call_id, tool_name)
        args_event = ToolCallArgsEvent.create(thread_id, tool_call_id, args_delta)
        end_event = ToolCallEndEvent.create(thread_id, tool_call_id, result=result_data)
        
        # Verify START event
        assert start_event.type == "TOOL_CALL_START"
        assert start_event.threadId == thread_id
        assert start_event.toolCallId == tool_call_id
        assert start_event.toolCallName == tool_name
        
        # Verify ARGS event
        assert args_event.type == "TOOL_CALL_ARGS"
        assert args_event.threadId == thread_id
        assert args_event.toolCallId == tool_call_id
        assert args_event.delta == args_delta
        
        # Verify END event
        assert end_event.type == "TOOL_CALL_END"
        assert end_event.threadId == thread_id
        assert end_event.toolCallId == tool_call_id
        assert end_event.result == result_data
        assert end_event.error is None
    
    def test_tool_call_error_event(self):
        """Test tool call END event with error"""
        # Setup
        thread_id = "test_thread_006"
        tool_call_id = "test_tool_002"
        error_data = {"type": "APIError", "message": "Search API unavailable"}
        
        # Execute
        event = ToolCallEndEvent.create(thread_id, tool_call_id, error=error_data)
        
        # Verify
        assert event.type == "TOOL_CALL_END"
        assert event.threadId == thread_id
        assert event.toolCallId == tool_call_id
        assert event.result is None
        assert event.error == error_data
    
    def test_state_snapshot_event(self):
        """Test STATE_SNAPSHOT event creation"""
        # Setup
        thread_id = "test_thread_007"
        state_data = {
            "analysis_phase": "research",
            "progress_percentage": 25.0,
            "company_name": "Apple Inc."
        }
        
        # Execute
        event = StateSnapshotEvent.create(thread_id, state_data)
        
        # Verify
        assert event.type == "STATE_SNAPSHOT"
        assert event.threadId == thread_id
        assert event.state == state_data
        assert isinstance(event.timestamp, datetime)
    
    def test_state_delta_event(self):
        """Test STATE_DELTA event with JSON Patch operations"""
        # Setup
        thread_id = "test_thread_008"
        patch_operations = [
            {"op": "replace", "path": "/progress_percentage", "value": 50.0},
            {"op": "replace", "path": "/analysis_phase", "value": "analysis"},
            {"op": "add", "path": "/current_operation", "value": "financial_metrics_analysis"}
        ]
        
        # Execute
        event = StateDeltaEvent.create(thread_id, patch_operations)
        
        # Verify
        assert event.type == "STATE_DELTA"
        assert event.threadId == thread_id
        assert event.delta == patch_operations
        assert len(event.delta) == 3
        
        # Verify JSON Patch operations
        operations = {op["path"]: op["value"] for op in event.delta if "value" in op}
        assert operations["/progress_percentage"] == 50.0
        assert operations["/analysis_phase"] == "analysis"
        assert operations["/current_operation"] == "financial_metrics_analysis"
    
    def test_event_factory_function(self):
        """Test create_event factory function"""
        # Test valid event creation
        event = create_event(
            "RUN_STARTED",
            threadId="test_thread",
            runId="test_run",
            input={"test": "data"}
        )
        assert isinstance(event, RunStartedEvent)
        assert event.type == "RUN_STARTED"
        
        # Test invalid event type
        with pytest.raises(ValueError, match="Unknown event type"):
            create_event("INVALID_EVENT_TYPE", threadId="test")
    
    def test_message_id_generation(self):
        """Test message ID generation"""
        # Generate multiple IDs
        id1 = generate_message_id()
        id2 = generate_message_id()
        
        # Verify format and uniqueness
        assert id1.startswith("msg_")
        assert id2.startswith("msg_")
        assert id1 != id2
        assert len(id1) == 16  # "msg_" + 12 hex chars
        assert len(id2) == 16
    
    def test_tool_call_id_generation(self):
        """Test tool call ID generation"""
        # Generate multiple IDs
        id1 = generate_tool_call_id()
        id2 = generate_tool_call_id()
        
        # Verify format and uniqueness
        assert id1.startswith("tool_")
        assert id2.startswith("tool_")
        assert id1 != id2
        assert len(id1) == 17  # "tool_" + 12 hex chars
        assert len(id2) == 17
    
    def test_event_serialization(self):
        """Test event serialization to dict"""
        # Setup
        event = RunStartedEvent.create(
            "test_thread",
            "test_run", 
            {"company": "Apple"},
            metadata_key="metadata_value"
        )
        
        # Execute
        event_dict = event.model_dump()
        
        # Verify
        assert isinstance(event_dict, dict)
        assert event_dict["type"] == "RUN_STARTED"
        assert event_dict["threadId"] == "test_thread"
        assert event_dict["runId"] == "test_run"
        assert event_dict["input"]["company"] == "Apple"
        assert event_dict["metadata"]["metadata_key"] == "metadata_value"
        assert "timestamp" in event_dict
    
    def test_event_timestamp_consistency(self):
        """Test that event timestamps are consistent and recent"""
        # Setup
        before_time = datetime.now()
        
        # Execute
        event = RunStartedEvent.create("test_thread", "test_run", {})
        
        # Verify
        after_time = datetime.now()
        assert before_time <= event.timestamp <= after_time
        
        # Test that different events have different timestamps
        import time
        time.sleep(0.001)  # Small delay
        event2 = RunStartedEvent.create("test_thread2", "test_run2", {})
        assert event.timestamp != event2.timestamp
