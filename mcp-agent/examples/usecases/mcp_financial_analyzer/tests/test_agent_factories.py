"""
Comprehensive tests for agent factory functions.
Tests the creation and configuration of research_agent, analyst_agent, and report_writer.
"""

import pytest
from unittest.mock import Mock, patch
from typing import List

# Mock atomic-agents imports for testing without installation
with patch.dict('sys.modules', {
    'atomic_agents': <PERSON><PERSON>(),
    'atomic_agents.agents': <PERSON><PERSON>(),
    'atomic_agents.agents.base_agent': <PERSON><PERSON>(),
    'atomic_agents.lib': <PERSON><PERSON>(),
    'atomic_agents.lib.components': <PERSON><PERSON>(),
    'atomic_agents.lib.components.agent_memory': <PERSON><PERSON>(),
    'atomic_agents.lib.components.system_prompt_generator': <PERSON><PERSON>(),
    'atomic_agents.lib.base': <PERSON><PERSON>(),
    'atomic_agents.lib.base.base_io_schema': <PERSON><PERSON>(),
    'instructor': <PERSON><PERSON>(),
}):
    # Import after mocking
    from agents.research_agent import create_research_agent
    from agents.analyst_agent import create_analyst_agent
    from agents.report_writer import create_report_writer


class TestResearchAgentFactory:
    """Test suite for research agent factory function"""
    
    @pytest.fixture
    def mock_dependencies(self):
        """Mock all dependencies for agent creation"""
        with patch('agents.research_agent.BaseAgentWrapper') as mock_wrapper:
            mock_agent = Mock()
            mock_agent.name = "search_finder"
            mock_agent.server_names = ["g-search", "fetch"]
            mock_wrapper.return_value = mock_agent
            yield mock_wrapper, mock_agent
    
    def test_create_research_agent_basic(self, mock_dependencies):
        """Test basic research agent creation"""
        mock_wrapper, mock_agent = mock_dependencies
        
        agent = create_research_agent("Apple Inc.")
        
        assert agent is not None
        assert agent.name == "search_finder"
        assert agent.server_names == ["g-search", "fetch"]
        mock_wrapper.assert_called_once()
    
    def test_create_research_agent_instruction_content(self, mock_dependencies):
        """Test that research agent instruction contains required elements"""
        mock_wrapper, mock_agent = mock_dependencies
        
        company_name = "Microsoft Corporation"
        create_research_agent(company_name)
        
        # Get the call arguments
        call_args = mock_wrapper.call_args
        assert call_args is not None
        
        # Check that instruction contains company name
        instruction = call_args[1]['instruction']  # keyword argument
        assert company_name in instruction
        
        # Check for required search queries
        assert "stock price today" in instruction
        assert "latest quarterly earnings" in instruction
        assert "financial news" in instruction
        assert "earnings expectations" in instruction
    
    def test_create_research_agent_server_configuration(self, mock_dependencies):
        """Test that research agent has correct server configuration"""
        mock_wrapper, mock_agent = mock_dependencies
        
        create_research_agent("Tesla Inc.")
        
        call_args = mock_wrapper.call_args
        assert call_args[1]['server_names'] == ["g-search", "fetch"]
        assert call_args[1]['name'] == "search_finder"
    
    def test_create_research_agent_different_companies(self, mock_dependencies):
        """Test research agent creation with different company names"""
        mock_wrapper, mock_agent = mock_dependencies
        
        companies = ["Apple Inc.", "Google", "Amazon", "Meta Platforms"]
        
        for company in companies:
            create_research_agent(company)
            
            # Check that each call includes the company name
            call_args = mock_wrapper.call_args
            instruction = call_args[1]['instruction']
            assert company in instruction


class TestAnalystAgentFactory:
    """Test suite for analyst agent factory function"""
    
    @pytest.fixture
    def mock_dependencies(self):
        """Mock all dependencies for agent creation"""
        with patch('agents.analyst_agent.BaseAgentWrapper') as mock_wrapper:
            mock_agent = Mock()
            mock_agent.name = "financial_analyst"
            mock_agent.server_names = ["fetch"]
            mock_wrapper.return_value = mock_agent
            yield mock_wrapper, mock_agent
    
    def test_create_analyst_agent_basic(self, mock_dependencies):
        """Test basic analyst agent creation"""
        mock_wrapper, mock_agent = mock_dependencies
        
        agent = create_analyst_agent("Apple Inc.")
        
        assert agent is not None
        assert agent.name == "financial_analyst"
        assert agent.server_names == ["fetch"]
        mock_wrapper.assert_called_once()
    
    def test_create_analyst_agent_instruction_content(self, mock_dependencies):
        """Test that analyst agent instruction contains required analysis criteria"""
        mock_wrapper, mock_agent = mock_dependencies
        
        company_name = "NVIDIA Corporation"
        create_analyst_agent(company_name)
        
        call_args = mock_wrapper.call_args
        instruction = call_args[1]['instruction']
        
        # Check company name inclusion
        assert company_name in instruction
        
        # Check for required analysis criteria
        assert "stock is up or down" in instruction
        assert "earnings beat or missed" in instruction
        assert "strengths and concerns" in instruction
        assert "analyst recommendations" in instruction
        assert "specific with numbers" in instruction
    
    def test_create_analyst_agent_server_configuration(self, mock_dependencies):
        """Test that analyst agent has correct server configuration"""
        mock_wrapper, mock_agent = mock_dependencies
        
        create_analyst_agent("Meta Platforms")
        
        call_args = mock_wrapper.call_args
        assert call_args[1]['server_names'] == ["fetch"]
        assert call_args[1]['name'] == "financial_analyst"


class TestReportWriterFactory:
    """Test suite for report writer factory function"""
    
    @pytest.fixture
    def mock_dependencies(self):
        """Mock all dependencies for agent creation"""
        with patch('agents.report_writer.BaseAgentWrapper') as mock_wrapper:
            mock_agent = Mock()
            mock_agent.name = "report_writer"
            mock_agent.server_names = ["filesystem"]
            mock_wrapper.return_value = mock_agent
            yield mock_wrapper, mock_agent
    
    def test_create_report_writer_basic(self, mock_dependencies):
        """Test basic report writer creation"""
        mock_wrapper, mock_agent = mock_dependencies
        
        agent = create_report_writer("Apple Inc.", "/tmp/apple_report.md")
        
        assert agent is not None
        assert agent.name == "report_writer"
        assert agent.server_names == ["filesystem"]
        mock_wrapper.assert_called_once()
    
    def test_create_report_writer_instruction_content(self, mock_dependencies):
        """Test that report writer instruction contains required sections"""
        mock_wrapper, mock_agent = mock_dependencies
        
        company_name = "Amazon.com Inc."
        output_path = "/reports/amazon_analysis.md"
        create_report_writer(company_name, output_path)
        
        call_args = mock_wrapper.call_args
        instruction = call_args[1]['instruction']
        
        # Check company name and output path inclusion
        assert company_name in instruction
        assert output_path in instruction
        
        # Check for required report sections
        assert "professional header" in instruction
        assert "company description" in instruction
        assert "stock performance" in instruction
        assert "earnings results" in instruction
        assert "recent news" in instruction
        assert "outlook and recommendation" in instruction
        assert "sources and references" in instruction
        
        # Check formatting requirements
        assert "markdown" in instruction
        assert "800 words" in instruction
    
    def test_create_report_writer_server_configuration(self, mock_dependencies):
        """Test that report writer has correct server configuration"""
        mock_wrapper, mock_agent = mock_dependencies
        
        create_report_writer("Tesla Inc.", "/tmp/tesla_report.md")
        
        call_args = mock_wrapper.call_args
        assert call_args[1]['server_names'] == ["filesystem"]
        assert call_args[1]['name'] == "report_writer"
    
    def test_create_report_writer_different_paths(self, mock_dependencies):
        """Test report writer creation with different output paths"""
        mock_wrapper, mock_agent = mock_dependencies
        
        test_cases = [
            ("Apple Inc.", "/reports/apple.md"),
            ("Google", "/tmp/google_analysis.md"),
            ("Microsoft", "/output/msft_report.md")
        ]
        
        for company, path in test_cases:
            create_report_writer(company, path)
            
            call_args = mock_wrapper.call_args
            instruction = call_args[1]['instruction']
            assert company in instruction
            assert path in instruction


class TestAgentFactoryIntegration:
    """Test suite for integration between different agent factories"""
    
    @pytest.fixture
    def mock_all_dependencies(self):
        """Mock all dependencies for all agent types"""
        with patch('agents.research_agent.BaseAgentWrapper') as mock_research, \
             patch('agents.analyst_agent.BaseAgentWrapper') as mock_analyst, \
             patch('agents.report_writer.BaseAgentWrapper') as mock_writer:
            
            # Configure mocks
            mock_research.return_value.name = "search_finder"
            mock_analyst.return_value.name = "financial_analyst"
            mock_writer.return_value.name = "report_writer"
            
            yield mock_research, mock_analyst, mock_writer
    
    def test_create_all_agents_for_workflow(self, mock_all_dependencies):
        """Test creating all agents needed for the financial analysis workflow"""
        mock_research, mock_analyst, mock_writer = mock_all_dependencies
        
        company_name = "Apple Inc."
        output_path = "/tmp/apple_report.md"
        
        # Create all agents
        research_agent = create_research_agent(company_name)
        analyst_agent = create_analyst_agent(company_name)
        report_writer = create_report_writer(company_name, output_path)
        
        # Verify all agents were created
        assert research_agent.name == "search_finder"
        assert analyst_agent.name == "financial_analyst"
        assert report_writer.name == "report_writer"
        
        # Verify all factories were called
        mock_research.assert_called_once()
        mock_analyst.assert_called_once()
        mock_writer.assert_called_once()
    
    def test_agent_name_uniqueness(self, mock_all_dependencies):
        """Test that all agents have unique names"""
        mock_research, mock_analyst, mock_writer = mock_all_dependencies
        
        research_agent = create_research_agent("Test Company")
        analyst_agent = create_analyst_agent("Test Company")
        report_writer = create_report_writer("Test Company", "/tmp/test.md")
        
        names = [research_agent.name, analyst_agent.name, report_writer.name]
        assert len(names) == len(set(names))  # All names should be unique
    
    def test_server_configuration_consistency(self, mock_all_dependencies):
        """Test that server configurations are appropriate for each agent type"""
        mock_research, mock_analyst, mock_writer = mock_all_dependencies
        
        # Get actual server configurations from factory calls
        create_research_agent("Test Company")
        research_servers = mock_research.call_args[1]['server_names']
        
        create_analyst_agent("Test Company")
        analyst_servers = mock_analyst.call_args[1]['server_names']
        
        create_report_writer("Test Company", "/tmp/test.md")
        writer_servers = mock_writer.call_args[1]['server_names']
        
        # Verify appropriate server assignments
        assert "g-search" in research_servers  # Research needs search
        assert "fetch" in research_servers     # Research needs web fetch
        assert "fetch" in analyst_servers      # Analyst needs web fetch
        assert "filesystem" in writer_servers  # Writer needs file system


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
