"""
Unit Tests for MCP to AG-UI Tool Bridge
======================================

Tests for tool execution bridging, event generation, and MCP integration.
"""

import pytest
import asyncio
from unittest.mock import Mo<PERSON>, As<PERSON><PERSON><PERSON>, call
import json

from ag_ui.tool_bridge import MCPToAGUIToolBridge
from ag_ui.events import ToolCallStartEvent, ToolCallArgsEvent, ToolCallEndEvent


class TestMCPToAGUIToolBridge:
    """Unit tests for MCP to AG-UI tool bridge"""
    
    def test_tool_mappings_initialization(self):
        """Test tool bridge initializes with correct financial tool mappings"""
        # Execute
        bridge = MCPToAGUIToolBridge()
        
        # Verify
        expected_mappings = {
            "g-search": "search_financial_data",
            "fetch": "fetch_web_content",
            "filesystem_read": "read_file",
            "filesystem_write": "write_file",
            "filesystem_list": "list_files"
        }
        assert bridge.tool_mappings == expected_mappings
    
    def test_get_tool_mapping(self):
        """Test get_tool_mapping method"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        
        # Test existing mapping
        assert bridge.get_tool_mapping("g-search") == "search_financial_data"
        assert bridge.get_tool_mapping("fetch") == "fetch_web_content"
        
        # Test non-existing mapping returns original name
        assert bridge.get_tool_mapping("unknown_tool") == "unknown_tool"
    
    def test_add_custom_tool_mapping(self):
        """Test adding custom tool mappings"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        
        # Execute
        bridge.add_tool_mapping("custom_mcp_tool", "custom_agui_tool")
        
        # Verify
        assert bridge.get_tool_mapping("custom_mcp_tool") == "custom_agui_tool"
        assert "custom_mcp_tool" in bridge.tool_mappings
    
    @pytest.mark.asyncio
    async def test_tool_execution_with_events_success(self):
        """Test successful tool execution with AG-UI event generation"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        observer = Mock()
        thread_id = "test_thread_001"
        tool_name = "g-search"
        tool_args = {"query": "Apple stock price", "search_type": "stock_price"}
        
        # Execute
        result = await bridge.execute_mcp_tool_with_agui_events(
            tool_name=tool_name,
            tool_args=tool_args,
            observer=observer,
            thread_id=thread_id
        )
        
        # Verify result
        assert "Search results for: Apple stock price" in result
        
        # Verify observer was called with correct events
        assert observer.call_count == 3  # START, ARGS, END
        
        # Verify TOOL_CALL_START event
        start_call = observer.call_args_list[0][0][0]
        assert start_call["type"] == "TOOL_CALL_START"
        assert start_call["threadId"] == thread_id
        assert start_call["toolCallName"] == "search_financial_data"  # Mapped name
        assert "toolCallId" in start_call
        
        # Verify TOOL_CALL_ARGS event
        args_call = observer.call_args_list[1][0][0]
        assert args_call["type"] == "TOOL_CALL_ARGS"
        assert start_call["toolCallId"] == args_call["toolCallId"]  # Same ID
        args_data = json.loads(args_call["delta"])
        assert args_data["query"] == "Apple stock price"
        assert args_data["search_type"] == "stock_price"
        
        # Verify TOOL_CALL_END event
        end_call = observer.call_args_list[2][0][0]
        assert end_call["type"] == "TOOL_CALL_END"
        assert start_call["toolCallId"] == end_call["toolCallId"]  # Same ID
        assert end_call["result"]["success"] is True
        assert "Search results for: Apple stock price" in end_call["result"]["data"]
        assert "execution_time" in end_call["result"]
        assert end_call["error"] is None
    
    @pytest.mark.asyncio
    async def test_tool_execution_with_error(self):
        """Test tool execution with error handling"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        observer = Mock()
        thread_id = "test_thread_002"
        
        # Mock context that raises exception
        context = Mock()
        context.call_tool = AsyncMock(side_effect=Exception("API Error"))
        
        # Execute and expect exception
        with pytest.raises(Exception, match="MCP tool execution failed"):
            await bridge.execute_mcp_tool_with_agui_events(
                tool_name="g-search",
                tool_args={"query": "test"},
                observer=observer,
                thread_id=thread_id,
                context=context
            )
        
        # Verify observer was called with error event
        assert observer.call_count == 3  # START, ARGS, END (with error)
        
        # Verify TOOL_CALL_END event contains error
        end_call = observer.call_args_list[2][0][0]
        assert end_call["type"] == "TOOL_CALL_END"
        assert end_call["result"] is None
        assert end_call["error"]["type"] == "Exception"
        assert "MCP tool execution failed" in end_call["error"]["message"]
    
    @pytest.mark.asyncio
    async def test_simulate_g_search_tool(self):
        """Test g-search tool simulation"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        
        # Execute
        result = await bridge._simulate_tool_execution(
            "g-search",
            {"query": "Microsoft earnings", "search_type": "earnings"}
        )
        
        # Verify
        assert "Search results for: Microsoft earnings" in result
        assert "Mock financial data and news results" in result
    
    @pytest.mark.asyncio
    async def test_simulate_fetch_tool(self):
        """Test fetch tool simulation"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        
        # Execute
        result = await bridge._simulate_tool_execution(
            "fetch",
            {"url": "https://example.com/financial-report"}
        )
        
        # Verify
        assert "Fetched content from: https://example.com/financial-report" in result
        assert "Mock web content" in result
    
    @pytest.mark.asyncio
    async def test_simulate_filesystem_tools(self):
        """Test filesystem tool simulations"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        
        # Test read
        result = await bridge._simulate_tool_execution(
            "filesystem_read",
            {"path": "/path/to/file.txt"}
        )
        assert "Mock file content from: /path/to/file.txt" in result
        
        # Test write
        result = await bridge._simulate_tool_execution(
            "filesystem_write",
            {"path": "/path/to/output.txt", "content": "test content"}
        )
        assert "Successfully wrote to: /path/to/output.txt" in result
        
        # Test list
        result = await bridge._simulate_tool_execution(
            "filesystem_list",
            {"path": "/path/to/directory"}
        )
        assert "Mock directory listing" in result
    
    @pytest.mark.asyncio
    async def test_active_tool_calls_tracking(self):
        """Test tracking of active tool calls"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        observer = Mock()
        thread_id = "test_thread_003"
        
        # Start tool execution in background
        task = asyncio.create_task(
            bridge.execute_mcp_tool_with_agui_events(
                tool_name="g-search",
                tool_args={"query": "test"},
                observer=observer,
                thread_id=thread_id
            )
        )
        
        # Give it a moment to start
        await asyncio.sleep(0.01)
        
        # Check active tool calls during execution
        active_calls = bridge.get_active_tool_calls()
        
        # Wait for completion
        await task
        
        # Verify tool call was tracked during execution
        # Note: It should be cleaned up after completion
        final_active_calls = bridge.get_active_tool_calls()
        assert len(final_active_calls) == 0
    
    @pytest.mark.asyncio
    async def test_mcp_context_integration(self):
        """Test integration with MCP context"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        observer = Mock()
        thread_id = "test_thread_004"
        
        # Mock MCP context
        context = Mock()
        context.call_tool = AsyncMock(return_value="MCP tool result")
        
        # Execute
        result = await bridge.execute_mcp_tool_with_agui_events(
            tool_name="g-search",
            tool_args={"query": "test"},
            observer=observer,
            thread_id=thread_id,
            context=context
        )
        
        # Verify MCP context was used
        context.call_tool.assert_called_once_with("g-search", {"query": "test"})
        assert result == "MCP tool result"
        
        # Verify events were still generated
        assert observer.call_count == 3
    
    @pytest.mark.asyncio
    async def test_mcp_context_with_tools_attribute(self):
        """Test MCP context with tools attribute"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        observer = Mock()
        thread_id = "test_thread_005"
        
        # Mock MCP context with tools attribute
        mock_tool = AsyncMock(return_value="Tool attribute result")
        context = Mock(spec=[])  # Empty spec to avoid default attributes
        context.tools = {"g-search": mock_tool}
        
        # Execute
        result = await bridge.execute_mcp_tool_with_agui_events(
            tool_name="g-search",
            tool_args={"query": "test"},
            observer=observer,
            thread_id=thread_id,
            context=context
        )
        
        # Verify tool was called correctly
        mock_tool.assert_called_once_with(query="test")
        assert result == "Tool attribute result"
    
    def test_get_financial_analysis_tools(self):
        """Test get_financial_analysis_tools method"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        
        # Execute
        tools = bridge.get_financial_analysis_tools()
        
        # Verify
        assert len(tools) == 3
        
        # Check search_financial_data tool
        search_tool = next(t for t in tools if t["name"] == "search_financial_data")
        assert search_tool["description"] == "Search for financial data, stock prices, and market information"
        assert "query" in search_tool["parameters"]["properties"]
        assert "search_type" in search_tool["parameters"]["properties"]
        assert search_tool["parameters"]["required"] == ["query"]
        
        # Check fetch_web_content tool
        fetch_tool = next(t for t in tools if t["name"] == "fetch_web_content")
        assert fetch_tool["description"] == "Fetch content from financial websites and reports"
        assert "url" in fetch_tool["parameters"]["properties"]
        
        # Check write_file tool
        write_tool = next(t for t in tools if t["name"] == "write_file")
        assert write_tool["description"] == "Write financial analysis report to file"
        assert "path" in write_tool["parameters"]["properties"]
        assert "content" in write_tool["parameters"]["properties"]
    
    @pytest.mark.asyncio
    async def test_parent_message_id_handling(self):
        """Test tool execution with parent message ID"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        observer = Mock()
        thread_id = "test_thread_006"
        parent_message_id = "parent_msg_001"
        
        # Execute
        await bridge.execute_mcp_tool_with_agui_events(
            tool_name="g-search",
            tool_args={"query": "test"},
            observer=observer,
            thread_id=thread_id,
            parent_message_id=parent_message_id
        )
        
        # Verify parent message ID was included in START event
        start_call = observer.call_args_list[0][0][0]
        assert start_call["parentMessageId"] == parent_message_id
    
    @pytest.mark.asyncio
    async def test_tool_execution_timing(self):
        """Test that tool execution timing is recorded"""
        # Setup
        bridge = MCPToAGUIToolBridge()
        observer = Mock()
        thread_id = "test_thread_007"
        
        # Execute
        await bridge.execute_mcp_tool_with_agui_events(
            tool_name="g-search",
            tool_args={"query": "test"},
            observer=observer,
            thread_id=thread_id
        )
        
        # Verify execution time was recorded
        end_call = observer.call_args_list[2][0][0]
        execution_time = end_call["result"]["execution_time"]
        assert isinstance(execution_time, float)
        assert execution_time >= 0.0  # Should be non-negative
