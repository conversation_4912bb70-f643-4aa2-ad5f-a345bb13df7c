"""
Comprehensive tests for BaseAgentWrapper class.
Tests the wrapper functionality, schema validation, memory management, and MCP compatibility.
"""

import pytest
import asyncio
import os
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict, Any

# Mock atomic-agents imports for testing without installation
with patch.dict('sys.modules', {
    'atomic_agents': <PERSON><PERSON>(),
    'atomic_agents.agents': <PERSON><PERSON>(),
    'atomic_agents.agents.base_agent': <PERSON><PERSON>(),
    'atomic_agents.lib': <PERSON><PERSON>(),
    'atomic_agents.lib.components': <PERSON><PERSON>(),
    'atomic_agents.lib.components.agent_memory': <PERSON>ck(),
    'atomic_agents.lib.components.system_prompt_generator': <PERSON><PERSON>(),
    'atomic_agents.lib.base': <PERSON><PERSON>(),
    'atomic_agents.lib.base.base_io_schema': <PERSON><PERSON>(),
    'instructor': <PERSON><PERSON>(),
}):
    # Import after mocking
    from schemas.agent_schemas import InstructionInputSchema, InstructionOutputSchema
    from agents.base_agent_wrapper import BaseAgentWrapper


class TestBaseAgentWrapper:
    """Test suite for BaseAgentWrapper functionality"""
    
    @pytest.fixture
    def mock_openai_client(self):
        """Mock OpenAI client for testing"""
        with patch('openai.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_openai.return_value = mock_client
            yield mock_client
    
    @pytest.fixture
    def mock_instructor(self):
        """Mock instructor client for testing"""
        with patch('instructor.from_openai') as mock_instructor:
            mock_client = Mock()
            mock_instructor.return_value = mock_client
            yield mock_client
    
    @pytest.fixture
    def mock_base_agent(self):
        """Mock BaseAgent for testing"""
        mock_agent = Mock()
        mock_agent.run = Mock()
        mock_agent.memory = Mock()
        mock_agent.memory.get_history = Mock(return_value=[])
        mock_agent.memory.clear = Mock()
        return mock_agent
    
    @pytest.fixture
    def sample_wrapper(self, mock_instructor, mock_base_agent):
        """Create a sample BaseAgentWrapper for testing"""
        with patch('agents.base_agent_wrapper.BaseAgent') as mock_base_agent_class:
            mock_base_agent_class.return_value = mock_base_agent
            
            wrapper = BaseAgentWrapper(
                name="test_agent",
                instruction="Test instruction for financial analysis",
                server_names=["g-search", "fetch"]
            )
            return wrapper
    
    def test_wrapper_initialization(self, sample_wrapper):
        """Test that BaseAgentWrapper initializes correctly"""
        assert sample_wrapper.name == "test_agent"
        assert sample_wrapper.instruction == "Test instruction for financial analysis"
        assert sample_wrapper.server_names == ["g-search", "fetch"]
        assert hasattr(sample_wrapper, '_base_agent')
        assert hasattr(sample_wrapper, '_enhanced_mode')
        assert sample_wrapper._enhanced_mode is True
    
    def test_wrapper_inherits_from_agent(self, sample_wrapper):
        """Test that BaseAgentWrapper properly inherits from Agent"""
        from mcp_agent.agents.agent import Agent
        assert isinstance(sample_wrapper, Agent)
    
    def test_enable_enhanced_processing(self, sample_wrapper):
        """Test enabling/disabling enhanced processing"""
        # Test enabling
        sample_wrapper.enable_enhanced_processing(True)
        assert sample_wrapper._enhanced_mode is True
        
        # Test disabling
        sample_wrapper.enable_enhanced_processing(False)
        assert sample_wrapper._enhanced_mode is False
    
    @pytest.mark.asyncio
    async def test_process_with_base_agent_success(self, sample_wrapper):
        """Test successful processing with BaseAgent"""
        # Mock successful BaseAgent response
        mock_response = Mock()
        mock_response.response = "Processed financial analysis result"
        sample_wrapper._base_agent.run.return_value = mock_response
        
        result = await sample_wrapper.process_with_base_agent(
            message="Analyze Apple stock",
            context="Current market conditions"
        )
        
        assert result == "Processed financial analysis result"
        sample_wrapper._base_agent.run.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_with_base_agent_disabled(self, sample_wrapper):
        """Test processing when enhanced mode is disabled"""
        sample_wrapper.enable_enhanced_processing(False)
        
        result = await sample_wrapper.process_with_base_agent("Test message")
        assert result == "Test message"  # Should return original message
    
    @pytest.mark.asyncio
    async def test_process_with_base_agent_fallback(self, sample_wrapper):
        """Test fallback behavior when BaseAgent fails"""
        # Mock BaseAgent to raise exception
        sample_wrapper._base_agent.run.side_effect = Exception("BaseAgent error")
        
        with patch('builtins.print') as mock_print:
            result = await sample_wrapper.process_with_base_agent("Test message")
            assert result == "Test message"  # Should fallback to original
            mock_print.assert_called_once()
    
    def test_get_conversation_history(self, sample_wrapper):
        """Test getting conversation history from BaseAgent memory"""
        mock_history = [
            {"role": "user", "content": "Test message"},
            {"role": "assistant", "content": "Test response"}
        ]
        sample_wrapper._base_agent.memory.get_history.return_value = mock_history
        
        history = sample_wrapper.get_conversation_history()
        assert history == mock_history
        sample_wrapper._base_agent.memory.get_history.assert_called_once()
    
    def test_get_conversation_history_no_memory(self, sample_wrapper):
        """Test getting conversation history when memory is not available"""
        # Remove memory attribute
        delattr(sample_wrapper._base_agent, 'memory')
        
        history = sample_wrapper.get_conversation_history()
        assert history == []
    
    def test_clear_memory(self, sample_wrapper):
        """Test clearing BaseAgent memory"""
        sample_wrapper.clear_memory()
        sample_wrapper._base_agent.memory.clear.assert_called_once()
    
    def test_clear_memory_no_memory(self, sample_wrapper):
        """Test clearing memory when memory is not available"""
        # Remove memory attribute
        delattr(sample_wrapper._base_agent, 'memory')
        
        # Should not raise exception
        sample_wrapper.clear_memory()
    
    def test_mcp_server_compatibility(self, sample_wrapper):
        """Test that MCP server configuration is preserved"""
        assert sample_wrapper.server_names == ["g-search", "fetch"]
        assert hasattr(sample_wrapper, 'context')
        assert hasattr(sample_wrapper, 'connection_persistence')
    
    def test_agent_interface_compatibility(self, sample_wrapper):
        """Test that all required Agent interface methods are available"""
        # Check that key Agent methods are available
        assert hasattr(sample_wrapper, 'initialize')
        assert hasattr(sample_wrapper, 'shutdown')
        assert hasattr(sample_wrapper, 'call_tool')
        assert hasattr(sample_wrapper, 'get_capabilities')
        assert callable(getattr(sample_wrapper, 'initialize'))
        assert callable(getattr(sample_wrapper, 'shutdown'))
        assert callable(getattr(sample_wrapper, 'call_tool'))
        assert callable(getattr(sample_wrapper, 'get_capabilities'))


class TestSchemaValidation:
    """Test suite for schema validation functionality"""
    
    def test_instruction_input_schema_creation(self):
        """Test creating InstructionInputSchema with valid data"""
        schema = InstructionInputSchema(
            message="Analyze Apple stock performance",
            context="Q4 2024 earnings season"
        )
        assert schema.message == "Analyze Apple stock performance"
        assert schema.context == "Q4 2024 earnings season"
    
    def test_instruction_input_schema_required_fields(self):
        """Test that required fields are enforced"""
        with pytest.raises(Exception):  # Pydantic validation error
            InstructionInputSchema()  # Missing required 'message' field
    
    def test_instruction_input_schema_optional_fields(self):
        """Test that optional fields work correctly"""
        schema = InstructionInputSchema(message="Test message")
        assert schema.message == "Test message"
        assert schema.context is None
    
    def test_instruction_output_schema_creation(self):
        """Test creating InstructionOutputSchema with valid data"""
        schema = InstructionOutputSchema(
            response="Apple stock is up 2.5% today",
            reasoning="Based on positive earnings report"
        )
        assert schema.response == "Apple stock is up 2.5% today"
        assert schema.reasoning == "Based on positive earnings report"
    
    def test_instruction_output_schema_required_fields(self):
        """Test that required fields are enforced"""
        with pytest.raises(Exception):  # Pydantic validation error
            InstructionOutputSchema()  # Missing required 'response' field


class TestMemoryManagement:
    """Test suite for memory management functionality"""
    
    @pytest.fixture
    def wrapper_with_memory(self, mock_instructor):
        """Create wrapper with mocked memory for testing"""
        mock_base_agent = Mock()
        mock_memory = Mock()
        mock_memory.get_history.return_value = []
        mock_memory.clear.return_value = None
        mock_base_agent.memory = mock_memory
        
        with patch('agents.base_agent_wrapper.BaseAgent') as mock_base_agent_class:
            mock_base_agent_class.return_value = mock_base_agent
            
            wrapper = BaseAgentWrapper(
                name="memory_test_agent",
                instruction="Test memory functionality",
                server_names=["test"]
            )
            return wrapper, mock_memory
    
    def test_memory_initialization(self, wrapper_with_memory):
        """Test that memory is properly initialized"""
        wrapper, mock_memory = wrapper_with_memory
        assert hasattr(wrapper._base_agent, 'memory')
        assert wrapper._base_agent.memory == mock_memory
    
    def test_conversation_history_tracking(self, wrapper_with_memory):
        """Test that conversation history is tracked"""
        wrapper, mock_memory = wrapper_with_memory
        
        # Mock conversation history
        mock_history = [
            {"role": "user", "content": "What is Apple's stock price?"},
            {"role": "assistant", "content": "Apple's stock price is $150.25"}
        ]
        mock_memory.get_history.return_value = mock_history
        
        history = wrapper.get_conversation_history()
        assert len(history) == 2
        assert history[0]["role"] == "user"
        assert history[1]["role"] == "assistant"
    
    def test_memory_persistence(self, wrapper_with_memory):
        """Test that memory persists across interactions"""
        wrapper, mock_memory = wrapper_with_memory
        
        # Simulate multiple interactions
        mock_memory.get_history.return_value = [
            {"role": "user", "content": "First question"},
            {"role": "assistant", "content": "First answer"},
            {"role": "user", "content": "Second question"},
            {"role": "assistant", "content": "Second answer"}
        ]
        
        history = wrapper.get_conversation_history()
        assert len(history) == 4
    
    def test_memory_clearing(self, wrapper_with_memory):
        """Test that memory can be cleared"""
        wrapper, mock_memory = wrapper_with_memory
        
        wrapper.clear_memory()
        mock_memory.clear.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
