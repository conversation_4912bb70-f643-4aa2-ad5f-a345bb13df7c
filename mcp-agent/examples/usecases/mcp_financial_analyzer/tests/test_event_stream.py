"""
Unit Tests for Financial Analysis Event Stream
=============================================

Tests for event streaming infrastructure, text message streaming, and observer management.
"""

import pytest
import asyncio
from unittest.mock import Mock, call

from ag_ui.event_stream import FinancialAnalysisEventStream, EventObserver
from ag_ui.events import TextMessageStartEvent, TextMessageContentEvent, TextMessageEndEvent


class TestEventObserver:
    """Unit tests for EventObserver"""
    
    def test_observer_initialization(self):
        """Test observer initializes correctly"""
        # Test without callback
        observer = EventObserver()
        assert observer.events == []
        assert observer.callback is None
        assert observer.is_active is True
        
        # Test with callback
        callback = Mock()
        observer_with_callback = EventObserver(callback)
        assert observer_with_callback.callback == callback
    
    def test_observer_event_collection(self):
        """Test observer collects events correctly"""
        # Setup
        observer = EventObserver()
        event1 = {"type": "TEST_EVENT_1", "data": "test1"}
        event2 = {"type": "TEST_EVENT_2", "data": "test2"}
        
        # Execute
        observer.next(event1)
        observer.next(event2)
        
        # Verify
        events = observer.get_events()
        assert len(events) == 2
        assert events[0] == event1
        assert events[1] == event2
    
    def test_observer_callback_execution(self):
        """Test observer executes callback on events"""
        # Setup
        callback = Mock()
        observer = EventObserver(callback)
        event = {"type": "TEST_EVENT", "data": "test"}
        
        # Execute
        observer.next(event)
        
        # Verify
        callback.assert_called_once_with(event)
        assert len(observer.events) == 1
    
    def test_observer_callback_error_handling(self):
        """Test observer handles callback errors gracefully"""
        # Setup
        callback = Mock(side_effect=Exception("Callback error"))
        observer = EventObserver(callback)
        event = {"type": "TEST_EVENT", "data": "test"}
        
        # Execute - should not raise exception
        observer.next(event)
        
        # Verify event was still collected
        assert len(observer.events) == 1
        assert observer.events[0] == event
    
    def test_observer_completion(self):
        """Test observer completion"""
        # Setup
        observer = EventObserver()
        
        # Execute
        observer.complete()
        
        # Verify
        assert observer.is_active is False
        
        # Test that inactive observer doesn't process events
        observer.next({"type": "TEST_EVENT"})
        assert len(observer.events) == 0
    
    def test_observer_error_handling(self):
        """Test observer error handling"""
        # Setup
        observer = EventObserver()
        error = Exception("Test error")
        
        # Execute
        observer.error(error)
        
        # Verify
        assert observer.is_active is False


class TestFinancialAnalysisEventStream:
    """Unit tests for FinancialAnalysisEventStream"""
    
    def test_event_stream_initialization(self):
        """Test event stream initializes with correct defaults"""
        # Execute
        stream = FinancialAnalysisEventStream()
        
        # Verify
        assert stream.chunk_size == 50
        assert stream.chunk_delay == 0.05
        assert stream.observers == []
    
    def test_event_stream_custom_parameters(self):
        """Test event stream with custom parameters"""
        # Execute
        stream = FinancialAnalysisEventStream(chunk_size=100, chunk_delay=0.1)
        
        # Verify
        assert stream.chunk_size == 100
        assert stream.chunk_delay == 0.1
    
    def test_observer_management(self):
        """Test adding and removing observers"""
        # Setup
        stream = FinancialAnalysisEventStream()
        observer1 = EventObserver()
        observer2 = EventObserver()
        
        # Test adding observers
        stream.add_observer(observer1)
        stream.add_observer(observer2)
        assert len(stream.observers) == 2
        
        # Test removing observer
        stream.remove_observer(observer1)
        assert len(stream.observers) == 1
        assert observer2 in stream.observers
        assert observer1 not in stream.observers
    
    def test_event_emission(self):
        """Test event emission to observers"""
        # Setup
        stream = FinancialAnalysisEventStream()
        observer1 = EventObserver()
        observer2 = EventObserver()
        stream.add_observer(observer1)
        stream.add_observer(observer2)
        
        # Create test event
        event = TextMessageStartEvent.create("test_thread", "test_msg")
        
        # Execute
        stream.emit_event(event)
        
        # Verify both observers received the event
        assert len(observer1.events) == 1
        assert len(observer2.events) == 1
        assert observer1.events[0]["type"] == "TEXT_MESSAGE_START"
        assert observer2.events[0]["type"] == "TEXT_MESSAGE_START"
    
    @pytest.mark.asyncio
    async def test_text_message_streaming(self):
        """Test streaming text message with chunking"""
        # Setup
        stream = FinancialAnalysisEventStream(chunk_size=10, chunk_delay=0.01)
        observer = EventObserver()
        stream.add_observer(observer)
        
        content = "This is a test message that should be chunked into smaller pieces"
        thread_id = "test_thread_001"
        
        # Execute
        message_id = await stream.stream_text_message(
            content=content,
            thread_id=thread_id,
            role="assistant"
        )
        
        # Verify message ID was returned
        assert message_id.startswith("msg_")
        
        # Verify events were generated
        events = observer.get_events()
        assert len(events) >= 3  # At least START, CONTENT(s), END
        
        # Verify START event
        start_event = events[0]
        assert start_event["type"] == "TEXT_MESSAGE_START"
        assert start_event["threadId"] == thread_id
        assert start_event["messageId"] == message_id
        assert start_event["role"] == "assistant"
        
        # Verify CONTENT events
        content_events = [e for e in events if e["type"] == "TEXT_MESSAGE_CONTENT"]
        assert len(content_events) > 0
        
        # Reconstruct content from chunks
        reconstructed_content = "".join(e["delta"] for e in content_events)
        assert reconstructed_content == content
        
        # Verify END event
        end_event = events[-1]
        assert end_event["type"] == "TEXT_MESSAGE_END"
        assert end_event["threadId"] == thread_id
        assert end_event["messageId"] == message_id
    
    @pytest.mark.asyncio
    async def test_text_message_streaming_with_custom_message_id(self):
        """Test text message streaming with custom message ID"""
        # Setup
        stream = FinancialAnalysisEventStream()
        observer = EventObserver()
        stream.add_observer(observer)
        
        custom_message_id = "custom_msg_001"
        
        # Execute
        returned_id = await stream.stream_text_message(
            content="Test content",
            thread_id="test_thread",
            message_id=custom_message_id
        )
        
        # Verify custom message ID was used
        assert returned_id == custom_message_id
        
        # Verify events use custom message ID
        events = observer.get_events()
        for event in events:
            if "messageId" in event:
                assert event["messageId"] == custom_message_id
    
    @pytest.mark.asyncio
    async def test_analysis_phase_streaming(self):
        """Test streaming analysis phase information"""
        # Setup
        stream = FinancialAnalysisEventStream()
        observer = EventObserver()
        stream.add_observer(observer)
        
        # Execute
        message_id = await stream.stream_analysis_phase(
            phase_name="Research Phase",
            phase_description="Gathering financial data for Apple Inc.",
            thread_id="test_thread_002",
            progress=25.0
        )
        
        # Verify
        assert message_id.startswith("msg_")
        
        events = observer.get_events()
        assert len(events) >= 3  # START, CONTENT(s), END
        
        # Check metadata in events
        start_event = events[0]
        assert start_event["metadata"]["phase"] == "Research Phase"
        assert start_event["metadata"]["progress"] == 25.0
        assert start_event["metadata"]["message_type"] == "phase_update"
        
        # Check content includes phase name and progress
        content_events = [e for e in events if e["type"] == "TEXT_MESSAGE_CONTENT"]
        full_content = "".join(e["delta"] for e in content_events)
        assert "Research Phase" in full_content
        assert "25.0%" in full_content
        assert "Gathering financial data for Apple Inc." in full_content
    
    @pytest.mark.asyncio
    async def test_agent_activity_streaming(self):
        """Test streaming agent activity information"""
        # Setup
        stream = FinancialAnalysisEventStream()
        observer = EventObserver()
        stream.add_observer(observer)
        
        # Execute
        message_id = await stream.stream_agent_activity(
            agent_name="Research Agent",
            activity="Searching for financial data",
            thread_id="test_thread_003",
            details="Querying multiple financial APIs for comprehensive data"
        )
        
        # Verify
        events = observer.get_events()
        
        # Check metadata
        start_event = events[0]
        assert start_event["metadata"]["agent_name"] == "Research Agent"
        assert start_event["metadata"]["activity"] == "Searching for financial data"
        assert start_event["metadata"]["message_type"] == "agent_activity"
        
        # Check content
        content_events = [e for e in events if e["type"] == "TEXT_MESSAGE_CONTENT"]
        full_content = "".join(e["delta"] for e in content_events)
        assert "Research Agent" in full_content
        assert "Searching for financial data" in full_content
        assert "Querying multiple financial APIs" in full_content
    
    @pytest.mark.asyncio
    async def test_tool_execution_streaming(self):
        """Test streaming tool execution information"""
        # Setup
        stream = FinancialAnalysisEventStream()
        observer = EventObserver()
        stream.add_observer(observer)
        
        # Execute
        message_id = await stream.stream_tool_execution(
            tool_name="search_financial_data",
            operation="Searching for Apple stock data",
            thread_id="test_thread_004",
            details={"query": "AAPL stock price", "url": "https://api.example.com"}
        )
        
        # Verify
        events = observer.get_events()
        
        # Check metadata
        start_event = events[0]
        assert start_event["metadata"]["tool_name"] == "search_financial_data"
        assert start_event["metadata"]["operation"] == "Searching for Apple stock data"
        assert start_event["metadata"]["message_type"] == "tool_execution"
        assert start_event["metadata"]["details"]["query"] == "AAPL stock price"
        
        # Check content includes tool details
        content_events = [e for e in events if e["type"] == "TEXT_MESSAGE_CONTENT"]
        full_content = "".join(e["delta"] for e in content_events)
        assert "search_financial_data" in full_content
        assert "Searching for Apple stock data" in full_content
        assert "AAPL stock price" in full_content
        assert "https://api.example.com" in full_content
    
    @pytest.mark.asyncio
    async def test_results_streaming(self):
        """Test streaming analysis results"""
        # Setup
        stream = FinancialAnalysisEventStream()
        observer = EventObserver()
        stream.add_observer(observer)
        
        results = {
            "stock_price": 150.25,
            "market_cap": "2.4T",
            "pe_ratio": 28.5,
            "recommendations": ["BUY", "HOLD", "BUY"]
        }
        
        # Execute
        message_id = await stream.stream_results(
            results=results,
            thread_id="test_thread_005",
            result_type="financial_analysis"
        )
        
        # Verify
        events = observer.get_events()
        
        # Check metadata
        start_event = events[0]
        assert start_event["metadata"]["result_type"] == "financial_analysis"
        assert start_event["metadata"]["results"] == results
        assert start_event["metadata"]["message_type"] == "results"
        
        # Check content formatting
        content_events = [e for e in events if e["type"] == "TEXT_MESSAGE_CONTENT"]
        full_content = "".join(e["delta"] for e in content_events)
        assert "Financial Analysis" in full_content
        assert "**Stock Price**: 150.25" in full_content
        assert "**Market Cap**: 2.4T" in full_content
        assert "**Pe Ratio**: 28.5" in full_content
        assert "**Recommendations**: 3 items" in full_content
    
    def test_stream_completion(self):
        """Test completing all streams"""
        # Setup
        stream = FinancialAnalysisEventStream()
        observer1 = EventObserver()
        observer2 = EventObserver()
        stream.add_observer(observer1)
        stream.add_observer(observer2)
        
        # Execute
        stream.complete_stream()
        
        # Verify all observers are completed
        assert observer1.is_active is False
        assert observer2.is_active is False
    
    def test_stream_statistics(self):
        """Test getting stream statistics"""
        # Setup
        stream = FinancialAnalysisEventStream(chunk_size=25, chunk_delay=0.02)
        observer1 = EventObserver()
        observer2 = EventObserver()
        stream.add_observer(observer1)
        stream.add_observer(observer2)
        
        # Add some events
        observer1.next({"type": "TEST1"})
        observer1.next({"type": "TEST2"})
        observer2.next({"type": "TEST3"})
        
        # Execute
        stats = stream.get_stream_stats()
        
        # Verify
        assert stats["total_events"] == 3
        assert stats["active_observers"] == 2
        assert stats["total_observers"] == 2
        assert stats["chunk_size"] == 25
        assert stats["chunk_delay"] == 0.02
        
        # Complete one observer and check stats again
        observer1.complete()
        stats_after_completion = stream.get_stream_stats()
        assert stats_after_completion["active_observers"] == 1
        assert stats_after_completion["total_observers"] == 2
