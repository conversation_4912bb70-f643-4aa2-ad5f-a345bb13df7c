"""
Comprehensive integration tests for the BaseAgent wrapper integration.
Tests end-to-end workflow compatibility with orchestrator and evaluator components.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict, Any

# Mock atomic-agents imports for testing without installation
with patch.dict('sys.modules', {
    'atomic_agents': <PERSON><PERSON>(),
    'atomic_agents.agents': <PERSON><PERSON>(),
    'atomic_agents.agents.base_agent': <PERSON><PERSON>(),
    'atomic_agents.lib': <PERSON><PERSON>(),
    'atomic_agents.lib.components': <PERSON><PERSON>(),
    'atomic_agents.lib.components.agent_memory': <PERSON><PERSON>(),
    'atomic_agents.lib.components.system_prompt_generator': <PERSON><PERSON>(),
    'atomic_agents.lib.base': <PERSON><PERSON>(),
    'atomic_agents.lib.base.base_io_schema': <PERSON><PERSON>(),
    'instructor': <PERSON><PERSON>(),
}):
    # Import after mocking
    from agents.research_agent import create_research_agent
    from agents.analyst_agent import create_analyst_agent
    from agents.report_writer import create_report_writer


class TestOrchestratorCompatibility:
    """Test suite for orchestrator compatibility with BaseAgent wrappers"""
    
    @pytest.fixture
    def mock_orchestrator_dependencies(self):
        """Mock orchestrator and related components"""
        with patch('mcp_agent.workflows.orchestrator.orchestrator.Orchestrator') as mock_orchestrator, \
             patch('mcp_agent.workflows.evaluator_optimizer.evaluator_optimizer.EvaluatorOptimizerLLM') as mock_evaluator, \
             patch('mcp_agent.agents.agent.Agent') as mock_agent:
            
            mock_orchestrator_instance = Mock()
            mock_orchestrator.return_value = mock_orchestrator_instance
            
            mock_evaluator_instance = Mock()
            mock_evaluator.return_value = mock_evaluator_instance
            
            yield mock_orchestrator, mock_evaluator, mock_agent
    
    @pytest.fixture
    def mock_agent_factories(self):
        """Mock all agent factory functions"""
        with patch('agents.research_agent.BaseAgentWrapper') as mock_research_wrapper, \
             patch('agents.analyst_agent.BaseAgentWrapper') as mock_analyst_wrapper, \
             patch('agents.report_writer.BaseAgentWrapper') as mock_writer_wrapper:
            
            # Configure mock agents
            mock_research_agent = Mock()
            mock_research_agent.name = "search_finder"
            mock_research_agent.server_names = ["g-search", "fetch"]
            mock_research_wrapper.return_value = mock_research_agent
            
            mock_analyst_agent = Mock()
            mock_analyst_agent.name = "financial_analyst"
            mock_analyst_agent.server_names = ["fetch"]
            mock_analyst_wrapper.return_value = mock_analyst_agent
            
            mock_writer_agent = Mock()
            mock_writer_agent.name = "report_writer"
            mock_writer_agent.server_names = ["filesystem"]
            mock_writer_wrapper.return_value = mock_writer_agent
            
            yield mock_research_agent, mock_analyst_agent, mock_writer_agent
    
    def test_orchestrator_accepts_wrapped_agents(self, mock_orchestrator_dependencies, mock_agent_factories):
        """Test that orchestrator can accept BaseAgent wrapped agents"""
        mock_orchestrator, mock_evaluator, mock_agent = mock_orchestrator_dependencies
        research_agent, analyst_agent, writer_agent = mock_agent_factories
        
        # Create wrapped agents
        research = create_research_agent("Apple Inc.")
        analyst = create_analyst_agent("Apple Inc.")
        writer = create_report_writer("Apple Inc.", "/tmp/report.md")
        
        # Test that these can be passed to orchestrator
        from mcp_agent.workflows.orchestrator.orchestrator import Orchestrator
        
        # This should not raise an exception
        orchestrator = Orchestrator(
            llm_factory=Mock(),
            available_agents=[research, analyst, writer]
        )
        
        mock_orchestrator.assert_called_once()
    
    def test_evaluator_optimizer_accepts_wrapped_research_agent(self, mock_orchestrator_dependencies, mock_agent_factories):
        """Test that EvaluatorOptimizerLLM can accept wrapped research agent"""
        mock_orchestrator, mock_evaluator, mock_agent = mock_orchestrator_dependencies
        research_agent, _, _ = mock_agent_factories
        
        # Create wrapped research agent
        research = create_research_agent("Apple Inc.")
        
        # Create a mock evaluator agent (this would remain unchanged)
        evaluator = Mock()
        evaluator.name = "research_evaluator"
        
        # Test that wrapped agent can be used in EvaluatorOptimizerLLM
        from mcp_agent.workflows.evaluator_optimizer.evaluator_optimizer import EvaluatorOptimizerLLM
        
        quality_controller = EvaluatorOptimizerLLM(
            optimizer=research,
            evaluator=evaluator,
            llm_factory=Mock()
        )
        
        mock_evaluator.assert_called_once()
    
    def test_agent_interface_compatibility_with_orchestrator(self, mock_agent_factories):
        """Test that wrapped agents maintain Agent interface for orchestrator"""
        research_agent, analyst_agent, writer_agent = mock_agent_factories
        
        # Create wrapped agents
        research = create_research_agent("Apple Inc.")
        analyst = create_analyst_agent("Apple Inc.")
        writer = create_report_writer("Apple Inc.", "/tmp/report.md")
        
        # Test that all agents have required interface methods
        agents = [research, analyst, writer]
        required_methods = ['initialize', 'shutdown', 'call_tool', 'get_capabilities']
        
        for agent in agents:
            for method in required_methods:
                assert hasattr(agent, method), f"Agent {agent.name} missing method {method}"
                assert callable(getattr(agent, method)), f"Agent {agent.name} method {method} not callable"


class TestWorkflowIntegration:
    """Test suite for complete workflow integration"""
    
    @pytest.fixture
    def mock_complete_workflow(self):
        """Mock complete workflow components"""
        with patch('agents.research_agent.BaseAgentWrapper') as mock_research_wrapper, \
             patch('agents.analyst_agent.BaseAgentWrapper') as mock_analyst_wrapper, \
             patch('agents.report_writer.BaseAgentWrapper') as mock_writer_wrapper, \
             patch('mcp_agent.agents.agent.Agent') as mock_agent:
            
            # Configure wrapped agents
            mock_research = Mock()
            mock_research.name = "search_finder"
            mock_research.initialize = AsyncMock()
            mock_research.shutdown = AsyncMock()
            mock_research.call_tool = AsyncMock()
            mock_research_wrapper.return_value = mock_research
            
            mock_analyst = Mock()
            mock_analyst.name = "financial_analyst"
            mock_analyst.initialize = AsyncMock()
            mock_analyst.shutdown = AsyncMock()
            mock_analyst.call_tool = AsyncMock()
            mock_analyst_wrapper.return_value = mock_analyst
            
            mock_writer = Mock()
            mock_writer.name = "report_writer"
            mock_writer.initialize = AsyncMock()
            mock_writer.shutdown = AsyncMock()
            mock_writer.call_tool = AsyncMock()
            mock_writer_wrapper.return_value = mock_writer
            
            # Configure evaluator agent (unchanged)
            mock_evaluator = Mock()
            mock_evaluator.name = "research_evaluator"
            mock_agent.return_value = mock_evaluator
            
            yield mock_research, mock_analyst, mock_writer, mock_evaluator
    
    @pytest.mark.asyncio
    async def test_complete_workflow_initialization(self, mock_complete_workflow):
        """Test that complete workflow can be initialized with wrapped agents"""
        research, analyst, writer, evaluator = mock_complete_workflow
        
        # Create all agents for workflow
        research_agent = create_research_agent("Apple Inc.")
        analyst_agent = create_analyst_agent("Apple Inc.")
        report_writer = create_report_writer("Apple Inc.", "/tmp/report.md")
        
        # Test initialization
        await research_agent.initialize()
        await analyst_agent.initialize()
        await report_writer.initialize()
        
        # Verify initialization was called
        research.initialize.assert_called_once()
        analyst.initialize.assert_called_once()
        writer.initialize.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_complete_workflow_shutdown(self, mock_complete_workflow):
        """Test that complete workflow can be shut down properly"""
        research, analyst, writer, evaluator = mock_complete_workflow
        
        # Create all agents for workflow
        research_agent = create_research_agent("Apple Inc.")
        analyst_agent = create_analyst_agent("Apple Inc.")
        report_writer = create_report_writer("Apple Inc.", "/tmp/report.md")
        
        # Test shutdown
        await research_agent.shutdown()
        await analyst_agent.shutdown()
        await report_writer.shutdown()
        
        # Verify shutdown was called
        research.shutdown.assert_called_once()
        analyst.shutdown.assert_called_once()
        writer.shutdown.assert_called_once()
    
    def test_workflow_agent_configuration(self, mock_complete_workflow):
        """Test that workflow agents are configured correctly"""
        research, analyst, writer, evaluator = mock_complete_workflow
        
        # Create workflow agents
        research_agent = create_research_agent("Tesla Inc.")
        analyst_agent = create_analyst_agent("Tesla Inc.")
        report_writer = create_report_writer("Tesla Inc.", "/reports/tesla.md")
        
        # Verify agent names match expected workflow names
        assert research_agent.name == "search_finder"
        assert analyst_agent.name == "financial_analyst"
        assert report_writer.name == "report_writer"
        
        # Verify server configurations
        assert research_agent.server_names == ["g-search", "fetch"]
        assert analyst_agent.server_names == ["fetch"]
        assert report_writer.server_names == ["filesystem"]


class TestMemoryAndSchemaIntegration:
    """Test suite for memory management and schema validation in workflow context"""
    
    @pytest.fixture
    def mock_agents_with_memory(self):
        """Mock agents with memory functionality"""
        with patch('agents.research_agent.BaseAgentWrapper') as mock_wrapper:
            mock_agent = Mock()
            mock_agent.name = "search_finder"
            mock_agent.get_conversation_history = Mock(return_value=[])
            mock_agent.clear_memory = Mock()
            mock_agent.process_with_base_agent = AsyncMock(return_value="Enhanced response")
            mock_wrapper.return_value = mock_agent
            yield mock_agent
    
    def test_memory_functionality_in_workflow(self, mock_agents_with_memory):
        """Test that memory functionality works in workflow context"""
        mock_agent = mock_agents_with_memory
        
        # Create agent with memory
        research_agent = create_research_agent("Apple Inc.")
        
        # Test memory operations
        history = research_agent.get_conversation_history()
        assert history == []
        
        research_agent.clear_memory()
        mock_agent.clear_memory.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_enhanced_processing_in_workflow(self, mock_agents_with_memory):
        """Test that enhanced processing works in workflow context"""
        mock_agent = mock_agents_with_memory
        
        # Create agent with enhanced processing
        research_agent = create_research_agent("Apple Inc.")
        
        # Test enhanced processing
        result = await research_agent.process_with_base_agent(
            "Find Apple's latest earnings data",
            "Q4 2024 earnings season"
        )
        
        assert result == "Enhanced response"
        mock_agent.process_with_base_agent.assert_called_once()


class TestBackwardCompatibility:
    """Test suite for backward compatibility with existing workflow"""
    
    def test_agent_interface_unchanged(self):
        """Test that Agent interface remains unchanged for existing code"""
        # This test ensures that existing code using Agent class still works
        from mcp_agent.agents.agent import Agent
        
        # Create traditional agent
        traditional_agent = Agent(
            name="test_agent",
            instruction="Test instruction",
            server_names=["test"]
        )
        
        # Verify interface
        assert hasattr(traditional_agent, 'name')
        assert hasattr(traditional_agent, 'instruction')
        assert hasattr(traditional_agent, 'server_names')
        assert hasattr(traditional_agent, 'initialize')
        assert hasattr(traditional_agent, 'shutdown')
    
    def test_wrapped_agents_inherit_agent_interface(self):
        """Test that wrapped agents inherit full Agent interface"""
        with patch('agents.research_agent.BaseAgentWrapper') as mock_wrapper:
            mock_agent = Mock()
            # Ensure mock has all Agent attributes
            mock_agent.name = "search_finder"
            mock_agent.instruction = "Test instruction"
            mock_agent.server_names = ["g-search", "fetch"]
            mock_wrapper.return_value = mock_agent
            
            # Create wrapped agent
            wrapped_agent = create_research_agent("Apple Inc.")
            
            # Verify it has Agent interface
            assert hasattr(wrapped_agent, 'name')
            assert hasattr(wrapped_agent, 'instruction')
            assert hasattr(wrapped_agent, 'server_names')
    
    def test_mixed_agent_types_in_workflow(self):
        """Test that traditional and wrapped agents can coexist"""
        from mcp_agent.agents.agent import Agent
        
        # Create traditional agent
        traditional_agent = Agent(
            name="traditional_evaluator",
            instruction="Traditional evaluation",
            server_names=["test"]
        )
        
        with patch('agents.research_agent.BaseAgentWrapper') as mock_wrapper:
            mock_agent = Mock()
            mock_agent.name = "search_finder"
            mock_wrapper.return_value = mock_agent
            
            # Create wrapped agent
            wrapped_agent = create_research_agent("Apple Inc.")
            
            # Both should be compatible in a list
            agents = [traditional_agent, wrapped_agent]
            
            # Both should have compatible interfaces
            for agent in agents:
                assert hasattr(agent, 'name')
                assert hasattr(agent, 'server_names')


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
