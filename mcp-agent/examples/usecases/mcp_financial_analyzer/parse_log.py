#!/usr/bin/env python3
"""
JSONL Log Parser for MCP Agent Financial Analyzer
Extracts and structures all log entries from the JSONL log file.
"""

import json
import sys
from datetime import datetime
from typing import List, Dict, Any
import re

def parse_jsonl_log(file_path: str) -> List[Dict[str, Any]]:
    """Parse the JSONL log file and extract structured data."""
    log_entries = []
    
    with open(file_path, 'r', encoding='utf-8') as file:
        for line_num, line in enumerate(file, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                # Parse JSON entry
                entry = json.loads(line)
                
                # Extract and structure the data
                structured_entry = {
                    'line_number': line_num,
                    'timestamp': entry.get('timestamp', ''),
                    'level': entry.get('level', ''),
                    'namespace': entry.get('namespace', ''),
                    'message': entry.get('message', ''),
                    'data': entry.get('data', {}),
                    'raw_json': entry
                }
                
                log_entries.append(structured_entry)
                
            except json.JSONDecodeError as e:
                print(f"Error parsing line {line_num}: {e}", file=sys.stderr)
                continue
    
    return log_entries

def format_timestamp(timestamp_str: str) -> str:
    """Format timestamp for better readability."""
    try:
        dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        return dt.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # Remove microseconds beyond milliseconds
    except:
        return timestamp_str

def extract_key_data(entry: Dict[str, Any]) -> Dict[str, Any]:
    """Extract key data fields for analysis."""
    data = entry.get('data', {})
    key_info = {}
    
    # Extract progress information
    if isinstance(data, dict) and 'data' in data:
        inner_data = data['data']
        if isinstance(inner_data, dict):
            key_info.update({
                'progress_action': inner_data.get('progress_action'),
                'agent_name': inner_data.get('agent_name'),
                'model': inner_data.get('model'),
                'chat_turn': inner_data.get('chat_turn'),
                'tool_name': inner_data.get('tool_name'),
                'server_name': inner_data.get('server_name')
            })
    
    return {k: v for k, v in key_info.items() if v is not None}

def categorize_entry(entry: Dict[str, Any]) -> str:
    """Categorize log entry based on namespace and message content."""
    namespace = entry['namespace']
    message = entry['message'].lower()
    
    if 'mcp_connection_manager' in namespace:
        return 'MCP Connection'
    elif 'mcp_agent_client_session' in namespace:
        return 'MCP Client Session'
    elif 'mcp_aggregator' in namespace:
        return 'MCP Aggregator'
    elif 'workflows.llm' in namespace:
        return 'LLM Workflow'
    elif 'orchestrator' in namespace:
        return 'Orchestrator'
    elif 'evaluator_optimizer' in namespace:
        return 'Evaluator/Optimizer'
    elif 'agents.agent' in namespace:
        return 'Agent Management'
    elif 'unified_stock_analyzer' in namespace:
        return 'Stock Analyzer'
    else:
        return 'Other'

def generate_summary_report(log_entries: List[Dict[str, Any]]) -> str:
    """Generate a comprehensive summary report."""
    if not log_entries:
        return "No log entries found."
    
    # Basic statistics
    total_entries = len(log_entries)
    level_counts = {}
    category_counts = {}
    namespace_counts = {}
    
    # Time range
    timestamps = [entry['timestamp'] for entry in log_entries if entry['timestamp']]
    start_time = min(timestamps) if timestamps else 'Unknown'
    end_time = max(timestamps) if timestamps else 'Unknown'
    
    # Analyze entries
    for entry in log_entries:
        # Level counts
        level = entry['level']
        level_counts[level] = level_counts.get(level, 0) + 1
        
        # Category counts
        category = categorize_entry(entry)
        category_counts[category] = category_counts.get(category, 0) + 1
        
        # Namespace counts
        namespace = entry['namespace']
        namespace_counts[namespace] = namespace_counts.get(namespace, 0) + 1
    
    # Generate report
    report = f"""
# MCP Agent Financial Analyzer Log Analysis Report

## Summary Statistics
- **Total Log Entries**: {total_entries}
- **Time Range**: {format_timestamp(start_time)} to {format_timestamp(end_time)}
- **Duration**: {calculate_duration(start_time, end_time)}

## Log Level Distribution
"""
    
    for level, count in sorted(level_counts.items()):
        percentage = (count / total_entries) * 100
        report += f"- **{level}**: {count} entries ({percentage:.1f}%)\n"
    
    report += "\n## Category Distribution\n"
    for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / total_entries) * 100
        report += f"- **{category}**: {count} entries ({percentage:.1f}%)\n"
    
    report += "\n## Top Namespaces\n"
    top_namespaces = sorted(namespace_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    for namespace, count in top_namespaces:
        percentage = (count / total_entries) * 100
        report += f"- `{namespace}`: {count} entries ({percentage:.1f}%)\n"
    
    return report

def calculate_duration(start_time: str, end_time: str) -> str:
    """Calculate duration between start and end times."""
    try:
        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        duration = end_dt - start_dt
        
        total_seconds = int(duration.total_seconds())
        minutes = total_seconds // 60
        seconds = total_seconds % 60
        
        return f"{minutes}m {seconds}s"
    except:
        return "Unknown"

def export_structured_data(log_entries: List[Dict[str, Any]], output_file: str):
    """Export structured data to JSON file."""
    export_data = []
    
    for entry in log_entries:
        export_entry = {
            'line_number': entry['line_number'],
            'timestamp': entry['timestamp'],
            'formatted_timestamp': format_timestamp(entry['timestamp']),
            'level': entry['level'],
            'namespace': entry['namespace'],
            'message': entry['message'],
            'category': categorize_entry(entry),
            'key_data': extract_key_data(entry),
            'has_data': bool(entry['data']),
            'data_size': len(json.dumps(entry['data'])) if entry['data'] else 0
        }
        export_data.append(export_entry)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, indent=2, ensure_ascii=False)

def main():
    """Main function to process the log file."""
    log_file_path = '/merge/mcp-agent/examples/usecases/mcp_financial_analyzer/logs/mcp-agent-20250627_135847.jsonl'
    
    print("Parsing JSONL log file...")
    log_entries = parse_jsonl_log(log_file_path)
    
    if not log_entries:
        print("No log entries found.")
        return
    
    print(f"Successfully parsed {len(log_entries)} log entries")
    
    # Generate summary report
    print("\nGenerating summary report...")
    summary_report = generate_summary_report(log_entries)
    print(summary_report)
    
    # Export structured data
    output_file = '/merge/mcp-agent/examples/usecases/mcp_financial_analyzer/logs/parsed_log_data.json'
    print(f"\nExporting structured data to {output_file}...")
    export_structured_data(log_entries, output_file)
    
    # Show sample entries
    print("\n## Sample Log Entries")
    print("=" * 50)
    
    for i, entry in enumerate(log_entries[:5]):
        print(f"\n**Entry {entry['line_number']}** ({entry['level']})")
        print(f"**Time**: {format_timestamp(entry['timestamp'])}")
        print(f"**Namespace**: {entry['namespace']}")
        print(f"**Message**: {entry['message'][:200]}{'...' if len(entry['message']) > 200 else ''}")
        if entry['data']:
            print(f"**Data**: {json.dumps(entry['data'], indent=2)[:300]}{'...' if len(json.dumps(entry['data'])) > 300 else ''}")
        print("-" * 50)

if __name__ == "__main__":
    main()