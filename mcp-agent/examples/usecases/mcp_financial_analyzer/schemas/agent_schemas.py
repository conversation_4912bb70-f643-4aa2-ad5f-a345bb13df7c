"""
Schema definitions for BaseAgent wrapper integration.
Provides input/output schemas for instruction-based agents.
"""

from pydantic import Field, BaseModel
from typing import Optional

try:
    from atomic_agents.lib.base.base_io_schema import BaseIOSchema
except ImportError:
    # Fallback for testing without atomic-agents installed
    class BaseIOSchema(BaseModel):
        """Fallback BaseIOSchema for testing"""
        pass


class InstructionInputSchema(BaseIOSchema):
    """
    Generic input schema for instruction-based agents.
    Captures the message and optional context for processing.
    """
    message: str = Field(
        ..., 
        description="Input message or task for the agent to process"
    )
    context: Optional[str] = Field(
        None, 
        description="Additional context information to help with processing"
    )


class InstructionOutputSchema(BaseIOSchema):
    """
    Generic output schema for instruction-based agents.
    Provides the response and optional reasoning.
    """
    response: str = Field(
        ..., 
        description="Agent's response to the input message"
    )
    reasoning: Optional[str] = Field(
        None, 
        description="Agent's reasoning process or explanation"
    )


class FinancialResearchInputSchema(BaseIOSchema):
    """
    Specialized input schema for financial research agents.
    Extends basic instruction schema with financial-specific fields.
    """
    company_name: str = Field(
        ..., 
        description="Name of the company to research"
    )
    research_queries: list[str] = Field(
        default_factory=list,
        description="Specific search queries to execute"
    )
    focus_areas: list[str] = Field(
        default=["stock_price", "earnings", "news"],
        description="Areas to focus research on"
    )
    message: str = Field(
        ..., 
        description="Research instruction or task"
    )
    context: Optional[str] = Field(
        None, 
        description="Additional research context"
    )


class FinancialResearchOutputSchema(BaseIOSchema):
    """
    Specialized output schema for financial research results.
    Provides structured financial data with source attribution.
    """
    company_name: str = Field(
        ..., 
        description="Company name that was researched"
    )
    stock_data: Optional[dict] = Field(
        None, 
        description="Stock price and movement data"
    )
    earnings_data: Optional[dict] = Field(
        None, 
        description="Earnings information and metrics"
    )
    news_items: list[str] = Field(
        default_factory=list, 
        description="Recent news items and developments"
    )
    sources: list[str] = Field(
        default_factory=list, 
        description="Data sources and URLs"
    )
    response: str = Field(
        ..., 
        description="Comprehensive research response"
    )
    research_quality: Optional[str] = Field(
        None, 
        description="Self-assessed research quality rating"
    )


class FinancialAnalysisInputSchema(BaseIOSchema):
    """
    Input schema for financial analysis agents.
    Takes research data and analysis criteria.
    """
    company_name: str = Field(
        ..., 
        description="Company being analyzed"
    )
    research_data: str = Field(
        ..., 
        description="Raw research data to analyze"
    )
    analysis_criteria: list[str] = Field(
        default=[
            "stock_performance", 
            "earnings_analysis", 
            "strengths_concerns", 
            "recommendations"
        ],
        description="Analysis criteria to focus on"
    )
    message: str = Field(
        ..., 
        description="Analysis instruction or task"
    )
    context: Optional[str] = Field(
        None, 
        description="Additional analysis context"
    )


class FinancialAnalysisOutputSchema(BaseIOSchema):
    """
    Output schema for financial analysis results.
    Provides structured analysis with confidence metrics.
    """
    company_name: str = Field(
        ..., 
        description="Company that was analyzed"
    )
    stock_performance: Optional[str] = Field(
        None, 
        description="Stock performance analysis"
    )
    earnings_analysis: Optional[str] = Field(
        None, 
        description="Earnings performance analysis"
    )
    key_strengths: list[str] = Field(
        default_factory=list, 
        description="Identified company strengths"
    )
    key_concerns: list[str] = Field(
        default_factory=list, 
        description="Identified concerns or risks"
    )
    recommendations: Optional[str] = Field(
        None, 
        description="Analyst recommendations"
    )
    response: str = Field(
        ..., 
        description="Comprehensive analysis response"
    )
    confidence_score: Optional[float] = Field(
        None, 
        description="Analysis confidence score (0-1)",
        ge=0.0,
        le=1.0
    )


class ReportGenerationInputSchema(BaseIOSchema):
    """
    Input schema for report generation agents.
    Takes analysis data and formatting requirements.
    """
    company_name: str = Field(
        ..., 
        description="Company name for the report"
    )
    analysis_data: str = Field(
        ..., 
        description="Analysis data to include in report"
    )
    output_path: str = Field(
        ..., 
        description="File path for report output"
    )
    report_sections: list[str] = Field(
        default=[
            "header", "overview", "stock_performance", 
            "earnings", "news", "outlook", "sources"
        ],
        description="Sections to include in report"
    )
    message: str = Field(
        ..., 
        description="Report generation instruction"
    )
    context: Optional[str] = Field(
        None, 
        description="Additional report context"
    )


class ReportGenerationOutputSchema(BaseIOSchema):
    """
    Output schema for report generation results.
    Provides report metadata and success status.
    """
    company_name: str = Field(
        ..., 
        description="Company name in the report"
    )
    report_path: str = Field(
        ..., 
        description="Path to the generated report file"
    )
    report_content: str = Field(
        ..., 
        description="Generated report content"
    )
    sections_included: list[str] = Field(
        default_factory=list, 
        description="Sections that were included in the report"
    )
    word_count: Optional[int] = Field(
        None, 
        description="Report word count"
    )
    response: str = Field(
        ..., 
        description="Report generation response"
    )
    generation_success: bool = Field(
        True, 
        description="Whether report was successfully generated"
    )


# Export all schemas for easy importing
__all__ = [
    "InstructionInputSchema",
    "InstructionOutputSchema", 
    "FinancialResearchInputSchema",
    "FinancialResearchOutputSchema",
    "FinancialAnalysisInputSchema", 
    "FinancialAnalysisOutputSchema",
    "ReportGenerationInputSchema",
    "ReportGenerationOutputSchema"
]
