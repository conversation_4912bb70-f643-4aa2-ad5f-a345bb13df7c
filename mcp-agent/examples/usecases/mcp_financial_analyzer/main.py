"""
Stock Analyzer with Orchestrator and EvaluatorOptimizerLLM Workflow
------------------------------------------------------------
An integrated financial analysis tool using the latest orchestrator implementation
that now supports AugmentedLLM components directly.
"""

import asyncio
import os
import sys
from datetime import datetime
from mcp_agent.app import MC<PERSON><PERSON>
from mcp_agent.agents.agent import Agent
from mcp_agent.workflows.orchestrator.orchestrator import Orchestrator
from mcp_agent.workflows.llm.augmented_llm import RequestParams
from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLLM
from mcp_agent.workflows.evaluator_optimizer.evaluator_optimizer import (
    EvaluatorOptimizerLLM,
    QualityRating,
)

# Import our new agent factories
from agents.research_agent import create_research_agent
from agents.analyst_agent import create_analyst_agent
from agents.report_writer import create_report_writer

# Configuration values
OUTPUT_DIR = "company_reports"

def get_company_name():
    """Get and validate company name from command line arguments."""
    if len(sys.argv) <= 1:
        return "Apple"

    company_arg = sys.argv[1]

    # Validate company name
    if company_arg.startswith('--') or company_arg.lower() in ['--company', 'company']:
        print("Error: Please provide a valid company name, not a placeholder or flag")
        print("Usage: python main.py 'Apple Inc.'")
        sys.exit(1)

    return company_arg

COMPANY_NAME = get_company_name()
MAX_ITERATIONS = 3

# Initialize app
app = MCPApp(name="unified_stock_analyzer", human_input_callback=None)


async def main():
    # Create output directory and set up file paths
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"{COMPANY_NAME.lower().replace(' ', '_')}_report_{timestamp}.md"
    output_path = os.path.join(OUTPUT_DIR, output_file)

    async with app.run() as analyzer_app:
        context = analyzer_app.context
        logger = analyzer_app.logger

        # Configure filesystem server to use current directory
        if "filesystem" in context.config.mcp.servers:
            context.config.mcp.servers["filesystem"].args.extend([os.getcwd()])
            logger.info("Filesystem server configured")
        else:
            logger.warning("Filesystem server not configured - report saving may fail")

        # Check for g-search server
        if "g-search" not in context.config.mcp.servers:
            logger.warning(
                "Google Search server not found! This script requires g-search-mcp"
            )
            logger.info("You can install it with: npm install -g g-search-mcp")
            return False

        # --- DEFINE AGENTS ---

        # Research agent: Collects data using Google Search
        # Using enhanced BaseAgentWrapper with atomic-agents capabilities
        research_agent = create_research_agent(COMPANY_NAME)

        # Research evaluator: Evaluates the quality of research
        research_evaluator = Agent(
            name="research_evaluator",
            instruction=f"""You are an expert research evaluator specializing in financial data quality.
            
            Evaluate the research data on {COMPANY_NAME} based on these criteria:
            
            1. Accuracy: Are facts properly cited with source URLs? Are numbers precise?
            2. Completeness: Is all required information present? (stock price, earnings data, recent news)
            3. Specificity: Are exact figures provided rather than generalizations?
            4. Clarity: Is the information organized and easy to understand?
            
            For each criterion, provide a rating:
            - EXCELLENT: Exceeds requirements, highly reliable
            - GOOD: Meets all requirements, reliable
            - FAIR: Missing some elements but usable
            - POOR: Missing critical information, not usable
            
            Provide an overall quality rating and specific feedback on what needs improvement.
            If any critical financial data is missing (stock price, earnings figures), the overall
            rating should not exceed FAIR.""",
        )

        # Create the research EvaluatorOptimizerLLM component
        research_quality_controller = EvaluatorOptimizerLLM(
            optimizer=research_agent,
            evaluator=research_evaluator,
            llm_factory=VLLMAugmentedLLM,
            min_rating=QualityRating.EXCELLENT,
        )

        # Analyst agent: Analyzes the research data
        # Using enhanced BaseAgentWrapper with atomic-agents capabilities
        analyst_agent = create_analyst_agent(COMPANY_NAME)

        # Report writer: Creates the final report
        # Using enhanced BaseAgentWrapper with atomic-agents capabilities
        report_writer = create_report_writer(COMPANY_NAME, output_path)

        # --- CREATE THE ORCHESTRATOR ---
        logger.info(f"Initializing stock analysis workflow for {COMPANY_NAME}")

        # The updated Orchestrator can now take AugmentedLLM instances directly
        orchestrator = Orchestrator(
            llm_factory=VLLMAugmentedLLM,
            available_agents=[
                # We can now pass the EvaluatorOptimizerLLM directly as a component
                research_quality_controller,
                analyst_agent,
                report_writer,
            ],
            plan_type="full",
        )

        # Define the task for the orchestrator
        task = f"""Create a high-quality stock analysis report for {COMPANY_NAME} by following these steps:

        1. Use the EvaluatorOptimizerLLM component (named 'research_quality_controller') to gather high-quality 
           financial data about {COMPANY_NAME}. This component will automatically evaluate 
           and improve the research until it reaches EXCELLENT quality.
           
           Ask for:
           - Current stock price and recent movement
           - Latest quarterly earnings results and performance vs expectations
           - Recent news and developments
        
        2. Use the financial_analyst to analyze this research data and identify key insights.
        
        3. Use the report_writer to create a comprehensive stock report and save it to:
           "{output_path}"
        
        The final report should be professional, fact-based, and include all relevant financial information."""

        # Validate company name before starting workflow
        if not COMPANY_NAME or COMPANY_NAME.startswith('--'):
            logger.error(f"Invalid company name: {COMPANY_NAME}")
            return False

        logger.info(f"Starting analysis for company: {COMPANY_NAME}")
        logger.info("Starting the stock analysis workflow")
        try:
            await orchestrator.generate_str(
                message=task, request_params=RequestParams(model="Qwen/Qwen3-32B")
            )

            # Check if report was successfully created
            if os.path.exists(output_path):
                logger.info(f"Report successfully generated: {output_path}")
                return True
            else:
                logger.error(f"Failed to create report at {output_path}")
                return False

        except Exception as e:
            logger.error(f"Error during workflow execution: {str(e)}")
            return False


if __name__ == "__main__":
    asyncio.run(main())
