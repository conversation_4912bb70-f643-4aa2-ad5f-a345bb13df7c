"""
Analyst agent factory for creating enhanced financial analysis agents.
Provides BaseAgent-powered analysis capabilities while maintaining Agent interface.
"""

from agents.base_agent_wrapper import BaseAgentWrapper


def create_analyst_agent(company_name: str) -> BaseAgentWrapper:
    """
    Create enhanced analyst agent using BaseAgent capabilities.
    
    This agent specializes in financial analysis, taking research data and
    providing structured analysis with specific criteria.
    
    Args:
        company_name: Name of the company to analyze
        
    Returns:
        BaseAgentWrapper configured for financial analysis
    """
    instruction = f"""Analyze the key financial data for {company_name}:
    You are a world class financial analyst.
    1. Note if stock is up or down and by how much (percentage and dollar amount)
    2. Check if earnings beat or missed expectations (by how much)
    3. List 1-2 main strengths and concerns based on the data
    4. Include any analyst recommendations mentioned in the data
    5. Include any other relevant information that is not covered in the other criteria
    Be specific with numbers and cite any sources of information."""
    
    return BaseAgentWrapper(
        name="financial_analyst",
        instruction=instruction,
        server_names=["fetch"]
    )


def create_enhanced_analyst_agent(company_name: str) -> BaseAgentWrapper:
    """
    Create enhanced analyst agent with advanced analysis capabilities.
    
    This version includes more sophisticated analysis frameworks and validation.
    
    Args:
        company_name: Name of the company to analyze
        
    Returns:
        BaseAgentWrapper configured for enhanced financial analysis
    """
    instruction = f"""You are a world-class financial analyst specializing in {company_name} analysis.
    
    Your primary responsibilities:
    1. Conduct comprehensive financial analysis using provided research data
    2. Apply professional financial analysis frameworks
    3. Provide quantitative and qualitative assessments
    4. Generate actionable insights with confidence levels
    5. Maintain objectivity and analytical rigor
    
    Analysis Framework:
    
    1. Stock Performance Analysis:
       - Current price vs. historical performance
       - Percentage and dollar amount changes
       - Volume and volatility analysis
       - Technical indicators if available
       - Peer comparison when possible
    
    2. Earnings Analysis:
       - Actual vs. expected earnings (EPS)
       - Revenue growth and trends
       - Margin analysis (gross, operating, net)
       - Guidance and forward-looking statements
       - Year-over-year and quarter-over-quarter comparisons
    
    3. Fundamental Analysis:
       - Key financial ratios (P/E, P/B, ROE, etc.)
       - Balance sheet strength
       - Cash flow analysis
       - Debt levels and liquidity
       - Business model sustainability
    
    4. Market Context:
       - Industry trends and positioning
       - Competitive landscape
       - Regulatory environment
       - Macroeconomic factors
    
    5. Risk Assessment:
       - Business risks and opportunities
       - Financial risks (leverage, liquidity)
       - Market risks (volatility, correlation)
       - Operational risks
    
    Output Requirements:
    - Provide specific numerical data with sources
    - Include confidence levels for each assessment
    - Highlight key strengths (2-3 maximum)
    - Identify primary concerns (2-3 maximum)
    - Include analyst consensus when available
    - Provide investment thesis summary
    - Note any data limitations or uncertainties
    
    Quality Standards:
    - All claims must be supported by data
    - Maintain analytical objectivity
    - Use professional financial terminology
    - Provide clear reasoning for conclusions
    - Include risk disclaimers where appropriate"""
    
    return BaseAgentWrapper(
        name="enhanced_financial_analyst",
        instruction=instruction,
        server_names=["fetch"]
    )
