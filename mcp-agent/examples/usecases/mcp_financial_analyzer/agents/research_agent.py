"""
Research agent factory for creating enhanced financial research agents.
Provides BaseAgent-powered research capabilities while maintaining Agent interface.
"""

from agents.base_agent_wrapper import BaseAgentWrapper


def create_research_agent(company_name: str) -> BaseAgentWrapper:
    """
    Create enhanced research agent using BaseAgent capabilities.
    
    This agent specializes in financial research using Google Search and web fetch
    capabilities to gather comprehensive company information.
    
    Args:
        company_name: Name of the company to research
        
    Returns:
        BaseAgentWrapper configured for financial research
    """
    instruction = f"""Use Google Search to find information about {company_name} in the current month of May 2025:
    You are a world class research analyst.
    Execute these exact search queries:
    1. "{company_name} stock price today"
    2. "{company_name} latest quarterly earnings"
    3. "{company_name} financial news"
    4. "{company_name} earnings expectations"
    
    Extract the most relevant information about:
    - Current stock price and recent movement
    - Latest earnings report data
    - Any significant recent news with correct citations
    
    Be smart and concise. Keep responses short and focused on facts."""
    
    return BaseAgentWrapper(
        name="search_finder",
        instruction=instruction,
        server_names=["g-search", "fetch"]
    )


def create_enhanced_research_agent(company_name: str) -> BaseAgentWrapper:
    """
    Create enhanced research agent with additional financial research capabilities.
    
    This version includes more sophisticated research strategies and validation.
    
    Args:
        company_name: Name of the company to research
        
    Returns:
        BaseAgentWrapper configured for enhanced financial research
    """
    instruction = f"""You are a world-class financial research analyst specializing in {company_name}.
    
    Your primary responsibilities:
    1. Execute comprehensive financial research using Google Search
    2. Gather current stock price and movement data
    3. Collect latest earnings information and performance metrics
    4. Find recent financial news and developments
    5. Ensure all data is properly cited with source URLs
    
    Research Strategy:
    - Use specific, targeted search queries for maximum relevance
    - Focus on recent, reliable financial sources (Bloomberg, Reuters, Yahoo Finance, SEC filings)
    - Extract precise numerical data when available
    - Maintain objectivity and factual accuracy
    - Provide clear source attribution for all claims
    
    Search Queries to Execute:
    1. "{company_name} stock price today current"
    2. "{company_name} latest quarterly earnings results"
    3. "{company_name} financial news recent developments"
    4. "{company_name} earnings expectations analyst forecasts"
    5. "{company_name} SEC filings recent"
    
    Quality Standards:
    - All financial figures must be current and accurate
    - Sources must be reputable financial publications
    - Data must be properly timestamped
    - Avoid speculation or unverified claims
    - Include confidence level for each piece of information
    
    Output Format:
    - Organize findings by category (stock data, earnings, news)
    - Include source URLs for verification
    - Highlight any data quality concerns
    - Provide research completeness assessment"""
    
    return BaseAgentWrapper(
        name="enhanced_search_finder",
        instruction=instruction,
        server_names=["g-search", "fetch"]
    )
