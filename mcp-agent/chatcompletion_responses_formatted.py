#!/usr/bin/env python3
import json
import sys
from datetime import datetime

def format_chatcompletion_responses():
    print('# ChatCompletion Responses - Formatted')
    print('**Source:** /merge/mcp-agent/examples/usecases/mcp_financial_analyzer/logs/mcp-agent-20250625_131120.jsonl')
    print('**Focus:** vLLM ChatCompletion response messages and reasoning')
    print()

    chatcompletion_entries = []
    
    with open('/merge/mcp-agent/examples/usecases/mcp_financial_analyzer/logs/mcp-agent-20250625_131120.jsonl', 'r') as f:
        for line_num, line in enumerate(f, 1):
            try:
                entry = json.loads(line.strip())
                if entry.get('message') == 'vLLM ChatCompletion response:':
                    entry['_line_number'] = line_num
                    chatcompletion_entries.append(entry)
            except json.JSONDecodeError:
                continue

    print(f'## Found {len(chatcompletion_entries)} ChatCompletion Responses')
    print()

    for i, entry in enumerate(chatcompletion_entries, 1):
        line_num = entry.get('_line_number', '?')
        timestamp = entry.get('timestamp', 'N/A')
        namespace = entry.get('namespace', '')
        data = entry.get('data', {}).get('data', {})
        
        # Format timestamp
        try:
            if timestamp != 'N/A':
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                readable_time = dt.strftime('%H:%M:%S.%f')[:-3]
            else:
                readable_time = 'N/A'
        except:
            readable_time = timestamp
        
        print(f'---')
        print(f'## ChatCompletion Response #{i}')
        print(f'**Line:** {line_num} | **Time:** {readable_time}')
        print(f'**Agent:** `{namespace.split(".")[-1] if namespace else "unknown"}`')
        print()
        
        # Extract key information
        if 'choices' in data and data['choices']:
            choice = data['choices'][0]
            message = choice.get('message', {})
            content = message.get('content', '')
            reasoning = message.get('reasoning_content', '')
            tool_calls = message.get('tool_calls', [])
            finish_reason = choice.get('finish_reason', 'unknown')
            
            # Usage information
            usage = data.get('usage', {})
            prompt_tokens = usage.get('prompt_tokens', 0)
            completion_tokens = usage.get('completion_tokens', 0)
            total_tokens = usage.get('total_tokens', 0)
            
            print(f'### Response Summary')
            print(f'- **Finish Reason:** {finish_reason}')
            print(f'- **Token Usage:** {prompt_tokens} prompt + {completion_tokens} completion = {total_tokens} total')
            print(f'- **Model:** {data.get("model", "unknown")}')
            print()
            
            # Show reasoning content if available
            if reasoning and reasoning.strip():
                print(f'### Reasoning (Internal Thinking)')
                print('```')
                print(reasoning.strip())
                print('```')
                print()
            
            # Show tool calls if any
            if tool_calls:
                print(f'### Tool Calls ({len(tool_calls)})')
                for j, tool_call in enumerate(tool_calls, 1):
                    func_name = tool_call.get('function', {}).get('name', 'unknown')
                    func_args = tool_call.get('function', {}).get('arguments', '{}')
                    print(f'**Tool {j}:** `{func_name}`')
                    print('```json')
                    try:
                        args_obj = json.loads(func_args)
                        print(json.dumps(args_obj, indent=2))
                    except:
                        print(func_args)
                    print('```')
                print()
            
            # Show main content
            if content and content.strip():
                print(f'### Response Content')
                print('```')
                print(content.strip())
                print('```')
                print()
        
        print('---')
        print()

if __name__ == "__main__":
    format_chatcompletion_responses()