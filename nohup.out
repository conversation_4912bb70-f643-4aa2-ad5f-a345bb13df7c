INFO 06-23 13:03:57 [__init__.py:239] Automatically detected platform cuda.
INFO 06-23 13:04:09 [api_server.py:1043] vLLM API server version 0.8.5.post1
INFO 06-23 13:04:09 [api_server.py:1044] args: Namespace(subparser='serve', model_tag='Qwen/Qwen3-32B', config='', host='0.0.0.0', port=28701, uvicorn_log_level='info', disable_uvicorn_access_log=False, allow_credentials=False, allowed_origins=['*'], allowed_methods=['*'], allowed_headers=['*'], api_key='EMPTY', lora_modules=None, prompt_adapters=None, chat_template=None, chat_template_content_format='auto', response_role='assistant', ssl_keyfile=None, ssl_certfile=None, ssl_ca_certs=None, enable_ssl_refresh=False, ssl_cert_reqs=0, root_path=None, middleware=[], return_tokens_as_token_ids=False, disable_frontend_multiprocessing=False, enable_request_id_headers=False, enable_auto_tool_choice=True, tool_call_parser='hermes', tool_parser_plugin='', model='Qwen/Qwen3-32B', task='auto', tokenizer=None, hf_config_path=None, skip_tokenizer_init=False, revision=None, code_revision=None, tokenizer_revision=None, tokenizer_mode='auto', trust_remote_code=False, allowed_local_media_path=None, load_format='auto', download_dir=None, model_loader_extra_config={}, use_tqdm_on_load=True, config_format=<ConfigFormat.AUTO: 'auto'>, dtype='auto', max_model_len=None, guided_decoding_backend='auto', reasoning_parser='deepseek_r1', logits_processor_pattern=None, model_impl='auto', distributed_executor_backend=None, pipeline_parallel_size=1, tensor_parallel_size=1, data_parallel_size=1, enable_expert_parallel=False, max_parallel_loading_workers=None, ray_workers_use_nsight=False, disable_custom_all_reduce=False, block_size=None, gpu_memory_utilization=0.9, swap_space=4, kv_cache_dtype='auto', num_gpu_blocks_override=None, enable_prefix_caching=None, prefix_caching_hash_algo='builtin', cpu_offload_gb=0, calculate_kv_scales=False, disable_sliding_window=False, use_v2_block_manager=True, seed=None, max_logprobs=20, disable_log_stats=False, quantization=None, rope_scaling=None, rope_theta=None, hf_token=None, hf_overrides=None, enforce_eager=False, max_seq_len_to_capture=8192, tokenizer_pool_size=0, tokenizer_pool_type='ray', tokenizer_pool_extra_config={}, limit_mm_per_prompt={}, mm_processor_kwargs=None, disable_mm_preprocessor_cache=False, enable_lora=None, enable_lora_bias=False, max_loras=1, max_lora_rank=16, lora_extra_vocab_size=256, lora_dtype='auto', long_lora_scaling_factors=None, max_cpu_loras=None, fully_sharded_loras=False, enable_prompt_adapter=None, max_prompt_adapters=1, max_prompt_adapter_token=0, device='auto', speculative_config=None, ignore_patterns=[], served_model_name=None, qlora_adapter_name_or_path=None, show_hidden_metrics_for_version=None, otlp_traces_endpoint=None, collect_detailed_traces=None, disable_async_output_proc=False, max_num_batched_tokens=None, max_num_seqs=None, max_num_partial_prefills=1, max_long_partial_prefills=1, long_prefill_token_threshold=0, num_lookahead_slots=0, scheduler_delay_factor=0.0, preemption_mode=None, num_scheduler_steps=1, multi_step_stream_outputs=True, scheduling_policy='fcfs', enable_chunked_prefill=None, disable_chunked_mm_input=False, scheduler_cls='vllm.core.scheduler.Scheduler', override_neuron_config=None, override_pooler_config=None, compilation_config=None, kv_transfer_config=None, worker_cls='auto', worker_extension_cls='', generation_config='auto', override_generation_config=None, enable_sleep_mode=False, additional_config=None, enable_reasoning=True, disable_cascade_attn=False, disable_log_requests=False, max_log_len=None, disable_fastapi_docs=False, enable_prompt_tokens_details=False, enable_server_load_tracking=False, dispatch_function=<function ServeSubcommand.cmd at 0x7f4bb41fca40>)
INFO 06-23 13:04:32 [config.py:717] This model supports multiple tasks: {'generate', 'embed', 'classify', 'score', 'reward'}. Defaulting to 'generate'.
INFO 06-23 13:04:42 [config.py:2003] Chunked prefill is enabled with max_num_batched_tokens=2048.
INFO 06-23 13:04:59 [__init__.py:239] Automatically detected platform cuda.
INFO 06-23 13:05:09 [core.py:58] Initializing a V1 LLM engine (v0.8.5.post1) with config: model='Qwen/Qwen3-32B', speculative_config=None, tokenizer='Qwen/Qwen3-32B', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=40960, download_dir=None, load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto,  device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='auto', reasoning_backend='deepseek_r1'), observability_config=ObservabilityConfig(show_hidden_metrics=False, otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=None, served_model_name=Qwen/Qwen3-32B, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=True, chunked_prefill_enabled=True, use_async_output_proc=True, disable_mm_preprocessor_cache=False, mm_processor_kwargs=None, pooler_config=None, compilation_config={"level":3,"custom_ops":["none"],"splitting_ops":["vllm.unified_attention","vllm.unified_attention_with_output"],"use_inductor":true,"compile_sizes":[],"use_cudagraph":true,"cudagraph_num_of_warmups":1,"cudagraph_capture_sizes":[512,504,496,488,480,472,464,456,448,440,432,424,416,408,400,392,384,376,368,360,352,344,336,328,320,312,304,296,288,280,272,264,256,248,240,232,224,216,208,200,192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],"max_capture_size":512}
WARNING 06-23 13:05:10 [utils.py:2522] Methods determine_num_available_blocks,device_config,get_cache_block_size_bytes,initialize_cache not implemented in <vllm.v1.worker.gpu_worker.Worker object at 0x7f52b93434a0>
INFO 06-23 13:05:13 [parallel_state.py:1004] rank 0 in world size 1 is assigned as DP rank 0, PP rank 0, TP rank 0
INFO 06-23 13:05:13 [cuda.py:221] Using Flash Attention backend on V1 engine.
WARNING 06-23 13:05:13 [topk_topp_sampler.py:69] FlashInfer is not available. Falling back to the PyTorch-native implementation of top-p & top-k sampling. For the best performance, please install FlashInfer.
INFO 06-23 13:05:13 [gpu_model_runner.py:1329] Starting to load model Qwen/Qwen3-32B...
ERROR 06-23 13:05:14 [core.py:396] EngineCore failed to start.
ERROR 06-23 13:05:14 [core.py:396] Traceback (most recent call last):
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 387, in run_engine_core
ERROR 06-23 13:05:14 [core.py:396]     engine_core = EngineCoreProc(*args, **kwargs)
ERROR 06-23 13:05:14 [core.py:396]                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 329, in __init__
ERROR 06-23 13:05:14 [core.py:396]     super().__init__(vllm_config, executor_class, log_stats,
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 64, in __init__
ERROR 06-23 13:05:14 [core.py:396]     self.model_executor = executor_class(vllm_config)
ERROR 06-23 13:05:14 [core.py:396]                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/executor_base.py", line 52, in __init__
ERROR 06-23 13:05:14 [core.py:396]     self._init_executor()
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 47, in _init_executor
ERROR 06-23 13:05:14 [core.py:396]     self.collective_rpc("load_model")
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 56, in collective_rpc
ERROR 06-23 13:05:14 [core.py:396]     answer = run_method(self.driver_worker, method, args, kwargs)
ERROR 06-23 13:05:14 [core.py:396]              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/utils.py", line 2456, in run_method
ERROR 06-23 13:05:14 [core.py:396]     return func(*args, **kwargs)
ERROR 06-23 13:05:14 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_worker.py", line 162, in load_model
ERROR 06-23 13:05:14 [core.py:396]     self.model_runner.load_model()
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_model_runner.py", line 1332, in load_model
ERROR 06-23 13:05:14 [core.py:396]     self.model = get_model(vllm_config=self.vllm_config)
ERROR 06-23 13:05:14 [core.py:396]                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/__init__.py", line 14, in get_model
ERROR 06-23 13:05:14 [core.py:396]     return loader.load_model(vllm_config=vllm_config)
ERROR 06-23 13:05:14 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 452, in load_model
ERROR 06-23 13:05:14 [core.py:396]     model = _initialize_model(vllm_config=vllm_config)
ERROR 06-23 13:05:14 [core.py:396]             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 133, in _initialize_model
ERROR 06-23 13:05:14 [core.py:396]     return model_class(vllm_config=vllm_config, prefix=prefix)
ERROR 06-23 13:05:14 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py", line 269, in __init__
ERROR 06-23 13:05:14 [core.py:396]     self.model = Qwen3Model(vllm_config=vllm_config,
ERROR 06-23 13:05:14 [core.py:396]                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/compilation/decorators.py", line 151, in __init__
ERROR 06-23 13:05:14 [core.py:396]     old_init(self, vllm_config=vllm_config, prefix=prefix, **kwargs)
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py", line 241, in __init__
ERROR 06-23 13:05:14 [core.py:396]     super().__init__(vllm_config=vllm_config,
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/compilation/decorators.py", line 151, in __init__
ERROR 06-23 13:05:14 [core.py:396]     old_init(self, vllm_config=vllm_config, prefix=prefix, **kwargs)
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 305, in __init__
ERROR 06-23 13:05:14 [core.py:396]     self.start_layer, self.end_layer, self.layers = make_layers(
ERROR 06-23 13:05:14 [core.py:396]                                                     ^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 610, in make_layers
ERROR 06-23 13:05:14 [core.py:396]     maybe_offload_to_cpu(layer_fn(prefix=f"{prefix}.{idx}"))
ERROR 06-23 13:05:14 [core.py:396]                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 307, in <lambda>
ERROR 06-23 13:05:14 [core.py:396]     lambda prefix: decoder_layer_type(config=config,
ERROR 06-23 13:05:14 [core.py:396]                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py", line 172, in __init__
ERROR 06-23 13:05:14 [core.py:396]     self.self_attn = Qwen3Attention(
ERROR 06-23 13:05:14 [core.py:396]                      ^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py", line 100, in __init__
ERROR 06-23 13:05:14 [core.py:396]     self.o_proj = RowParallelLinear(
ERROR 06-23 13:05:14 [core.py:396]                   ^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py", line 1194, in __init__
ERROR 06-23 13:05:14 [core.py:396]     self.quant_method.create_weights(
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py", line 189, in create_weights
ERROR 06-23 13:05:14 [core.py:396]     weight = Parameter(torch.empty(sum(output_partition_sizes),
ERROR 06-23 13:05:14 [core.py:396]                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396]   File "/root/miniconda3/lib/python3.12/site-packages/torch/utils/_device.py", line 104, in __torch_function__
ERROR 06-23 13:05:14 [core.py:396]     return func(*args, **kwargs)
ERROR 06-23 13:05:14 [core.py:396]            ^^^^^^^^^^^^^^^^^^^^^
ERROR 06-23 13:05:14 [core.py:396] torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 80.00 MiB. GPU 0 has a total capacity of 39.39 GiB of which 78.06 MiB is free. Process 1644845 has 39.31 GiB memory in use. Of the allocated memory 38.82 GiB is allocated by PyTorch, and 18.49 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
Process EngineCore_0:
Traceback (most recent call last):
  File "/root/miniconda3/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/root/miniconda3/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 400, in run_engine_core
    raise e
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 387, in run_engine_core
    engine_core = EngineCoreProc(*args, **kwargs)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 329, in __init__
    super().__init__(vllm_config, executor_class, log_stats,
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core.py", line 64, in __init__
    self.model_executor = executor_class(vllm_config)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/executor_base.py", line 52, in __init__
    self._init_executor()
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 47, in _init_executor
    self.collective_rpc("load_model")
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/executor/uniproc_executor.py", line 56, in collective_rpc
    answer = run_method(self.driver_worker, method, args, kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/utils.py", line 2456, in run_method
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_worker.py", line 162, in load_model
    self.model_runner.load_model()
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/worker/gpu_model_runner.py", line 1332, in load_model
    self.model = get_model(vllm_config=self.vllm_config)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/__init__.py", line 14, in get_model
    return loader.load_model(vllm_config=vllm_config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 452, in load_model
    model = _initialize_model(vllm_config=vllm_config)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/model_loader/loader.py", line 133, in _initialize_model
    return model_class(vllm_config=vllm_config, prefix=prefix)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py", line 269, in __init__
    self.model = Qwen3Model(vllm_config=vllm_config,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/compilation/decorators.py", line 151, in __init__
    old_init(self, vllm_config=vllm_config, prefix=prefix, **kwargs)
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py", line 241, in __init__
    super().__init__(vllm_config=vllm_config,
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/compilation/decorators.py", line 151, in __init__
    old_init(self, vllm_config=vllm_config, prefix=prefix, **kwargs)
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 305, in __init__
    self.start_layer, self.end_layer, self.layers = make_layers(
                                                    ^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/utils.py", line 610, in make_layers
    maybe_offload_to_cpu(layer_fn(prefix=f"{prefix}.{idx}"))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen2.py", line 307, in <lambda>
    lambda prefix: decoder_layer_type(config=config,
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py", line 172, in __init__
    self.self_attn = Qwen3Attention(
                     ^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/models/qwen3.py", line 100, in __init__
    self.o_proj = RowParallelLinear(
                  ^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py", line 1194, in __init__
    self.quant_method.create_weights(
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/model_executor/layers/linear.py", line 189, in create_weights
    weight = Parameter(torch.empty(sum(output_partition_sizes),
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/torch/utils/_device.py", line 104, in __torch_function__
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 80.00 MiB. GPU 0 has a total capacity of 39.39 GiB of which 78.06 MiB is free. Process 1644845 has 39.31 GiB memory in use. Of the allocated memory 38.82 GiB is allocated by PyTorch, and 18.49 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
[rank0]:[W623 13:05:14.930275089 ProcessGroupNCCL.cpp:1496] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
Traceback (most recent call last):
  File "/root/miniconda3/bin/vllm", line 8, in <module>
    sys.exit(main())
             ^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/cli/main.py", line 53, in main
    args.dispatch_function(args)
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/cli/serve.py", line 27, in cmd
    uvloop.run(run_server(args))
  File "/root/miniconda3/lib/python3.12/site-packages/uvloop/__init__.py", line 109, in run
    return __asyncio.run(
           ^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/asyncio/runners.py", line 194, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/root/miniconda3/lib/python3.12/site-packages/uvloop/__init__.py", line 61, in wrapper
    return await main
           ^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/openai/api_server.py", line 1078, in run_server
    async with build_async_engine_client(args) as engine_client:
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/openai/api_server.py", line 146, in build_async_engine_client
    async with build_async_engine_client_from_engine_args(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/entrypoints/openai/api_server.py", line 178, in build_async_engine_client_from_engine_args
    async_llm = AsyncLLM.from_vllm_config(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/async_llm.py", line 150, in from_vllm_config
    return cls(
           ^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/async_llm.py", line 118, in __init__
    self.engine_core = core_client_class(
                       ^^^^^^^^^^^^^^^^^^
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core_client.py", line 642, in __init__
    super().__init__(
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core_client.py", line 398, in __init__
    self._wait_for_engine_startup()
  File "/root/miniconda3/lib/python3.12/site-packages/vllm/v1/engine/core_client.py", line 430, in _wait_for_engine_startup
    raise RuntimeError("Engine core initialization failed. "
RuntimeError: Engine core initialization failed. See root cause above.
